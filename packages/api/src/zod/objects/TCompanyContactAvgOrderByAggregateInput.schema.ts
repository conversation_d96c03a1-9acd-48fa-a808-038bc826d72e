/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TCompanyContactAvgOrderByAggregateInput>;
export const TCompanyContactAvgOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    vatRate: z.lazy(() => SortOrderSchema).optional(),
    capital: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    legalFormId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    accountingCode: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    familyId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    salesAccountCodeId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    skills: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    phoneCode: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    preferredBudgetsIds: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    collectiveAgreements: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
