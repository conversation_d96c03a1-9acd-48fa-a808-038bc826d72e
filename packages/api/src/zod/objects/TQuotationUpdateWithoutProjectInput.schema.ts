/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BoolFieldUpdateOperationsInputObjectSchema } from "./BoolFieldUpdateOperationsInput.schema";
import { LQuotationStatusUpdateOneRequiredWithoutQuotesNestedInputObjectSchema } from "./LQuotationStatusUpdateOneRequiredWithoutQuotesNestedInput.schema";
import { NullableBigIntFieldUpdateOperationsInputObjectSchema } from "./NullableBigIntFieldUpdateOperationsInput.schema";
import { NullableBoolFieldUpdateOperationsInputObjectSchema } from "./NullableBoolFieldUpdateOperationsInput.schema";
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from "./NullableDateTimeFieldUpdateOperationsInput.schema";
import { NullableFloatFieldUpdateOperationsInputObjectSchema } from "./NullableFloatFieldUpdateOperationsInput.schema";
import { NullableIntFieldUpdateOperationsInputObjectSchema } from "./NullableIntFieldUpdateOperationsInput.schema";
import { NullableStringFieldUpdateOperationsInputObjectSchema } from "./NullableStringFieldUpdateOperationsInput.schema";
import { OrganizationUpdateOneRequiredWithoutTQuotationNestedInputObjectSchema } from "./OrganizationUpdateOneRequiredWithoutTQuotationNestedInput.schema";
import { StringFieldUpdateOperationsInputObjectSchema } from "./StringFieldUpdateOperationsInput.schema";
import { TCompanyContactUpdateOneWithoutQuotationsNestedInputObjectSchema } from "./TCompanyContactUpdateOneWithoutQuotationsNestedInput.schema";
import { TGuyUpdateOneWithoutManagedQuotationsNestedInputObjectSchema } from "./TGuyUpdateOneWithoutManagedQuotationsNestedInput.schema";
import { TQuotationUpdatecompositionIdsInputObjectSchema } from "./TQuotationUpdatecompositionIdsInput.schema";
import { TQuotationUpdateemailIdsInputObjectSchema } from "./TQuotationUpdateemailIdsInput.schema";

type SchemaType = z.ZodType<Prisma.TQuotationUpdateWithoutProjectInput>;
export const TQuotationUpdateWithoutProjectInputObjectSchema: SchemaType = z
  .object({
    code: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    version: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    id: z
      .union([
        z.string(),
        z.lazy(() => StringFieldUpdateOperationsInputObjectSchema),
      ])
      .optional(),
    deleted: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    reference: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    secretToken: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    marketId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    description: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    locationId: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    clientName: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    clientAddress: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    clientCity: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    clientPostalCode: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    deadlineDate: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    deadlineTime: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    paymentConditionsId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    prorateRate: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    revisableMarket: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    validityId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    depositId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    firstPage: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    alignment: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    sentCompleted: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    numberOfPages: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    toBeWarranted: z
      .union([
        z.boolean(),
        z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema),
      ])
      .optional(),
    refused: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    index: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    rate: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    consultationId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    guyId: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    baseMarket: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    ceeAmount: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    ceeLabel: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    engagedWithoutAgreement: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    whoEngagedWithoutAgreement: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    engagementReason: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    keyId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    storageId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    newStorageId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    quotationDateTz: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    acceptanceDateTz: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    maxDurationMinutes: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    miningStatusId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    miningLocked: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailIds: z
      .union([
        z.lazy(() => TQuotationUpdateemailIdsInputObjectSchema),
        z.coerce.number().array(),
      ])
      .optional(),
    compositionIds: z
      .union([
        z.lazy(() => TQuotationUpdatecompositionIdsInputObjectSchema),
        z.coerce.number().array(),
      ])
      .optional(),
    organization: z
      .lazy(
        () =>
          OrganizationUpdateOneRequiredWithoutTQuotationNestedInputObjectSchema
      )
      .optional(),
    status: z
      .lazy(
        () =>
          LQuotationStatusUpdateOneRequiredWithoutQuotesNestedInputObjectSchema
      )
      .optional()
      .optional(),
    manager: z
      .lazy(() => TGuyUpdateOneWithoutManagedQuotationsNestedInputObjectSchema)
      .optional()
      .optional(),
    companyContact: z
      .lazy(
        () => TCompanyContactUpdateOneWithoutQuotationsNestedInputObjectSchema
      )
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
