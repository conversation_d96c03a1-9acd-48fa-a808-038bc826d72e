/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { NullableBigIntFieldUpdateOperationsInputObjectSchema } from "./NullableBigIntFieldUpdateOperationsInput.schema";
import { NullableBoolFieldUpdateOperationsInputObjectSchema } from "./NullableBoolFieldUpdateOperationsInput.schema";
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from "./NullableDateTimeFieldUpdateOperationsInput.schema";
import { NullableIntFieldUpdateOperationsInputObjectSchema } from "./NullableIntFieldUpdateOperationsInput.schema";
import { OrganizationUpdateOneRequiredWithoutTSalaryCostNestedInputObjectSchema } from "./OrganizationUpdateOneRequiredWithoutTSalaryCostNestedInput.schema";
import { StringFieldUpdateOperationsInputObjectSchema } from "./StringFieldUpdateOperationsInput.schema";

type SchemaType = z.ZodType<Prisma.TSalaryCostUpdateInput>;
export const TSalaryCostUpdateInputObjectSchema: SchemaType = z
  .object({
    id: z
      .union([
        z.string(),
        z.lazy(() => StringFieldUpdateOperationsInputObjectSchema),
      ])
      .optional(),
    deleted: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    totalMonthlyCost: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    collaboratorId: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    netMonthlySalary: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    grossMonthlySalary: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    incomeTax: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    month: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    year: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    monthId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    accountingCodeId: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    organization: z
      .lazy(
        () =>
          OrganizationUpdateOneRequiredWithoutTSalaryCostNestedInputObjectSchema
      )
      .optional(),
  })
  .strict() as SchemaType;
