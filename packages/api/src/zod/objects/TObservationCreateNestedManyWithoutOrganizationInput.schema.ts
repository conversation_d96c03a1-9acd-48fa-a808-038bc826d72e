/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TObservationCreateManyOrganizationInputEnvelopeObjectSchema } from "./TObservationCreateManyOrganizationInputEnvelope.schema";
import { TObservationCreateOrConnectWithoutOrganizationInputObjectSchema } from "./TObservationCreateOrConnectWithoutOrganizationInput.schema";
import { TObservationCreateWithoutOrganizationInputObjectSchema } from "./TObservationCreateWithoutOrganizationInput.schema";
import { TObservationWhereUniqueInputObjectSchema } from "./TObservationWhereUniqueInput.schema";

type SchemaType =
  z.ZodType<Prisma.TObservationCreateNestedManyWithoutOrganizationInput>;
export const TObservationCreateNestedManyWithoutOrganizationInputObjectSchema: SchemaType =
  z
    .object({
      create: z
        .union([
          z.lazy(() => TObservationCreateWithoutOrganizationInputObjectSchema),
          z
            .lazy(() => TObservationCreateWithoutOrganizationInputObjectSchema)
            .array(),
        ])
        .optional(),
      connectOrCreate: z
        .union([
          z.lazy(
            () =>
              TObservationCreateOrConnectWithoutOrganizationInputObjectSchema
          ),
          z
            .lazy(
              () =>
                TObservationCreateOrConnectWithoutOrganizationInputObjectSchema
            )
            .array(),
        ])
        .optional(),
      createMany: z
        .lazy(() => TObservationCreateManyOrganizationInputEnvelopeObjectSchema)
        .optional(),
      connect: z
        .union([
          z.lazy(() => TObservationWhereUniqueInputObjectSchema),
          z.lazy(() => TObservationWhereUniqueInputObjectSchema).array(),
        ])
        .optional(),
    })
    .strict() as SchemaType;
