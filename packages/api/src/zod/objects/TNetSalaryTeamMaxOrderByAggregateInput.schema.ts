/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TNetSalaryTeamMaxOrderByAggregateInput>;
export const TNetSalaryTeamMaxOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    id: z.lazy(() => SortOrderSchema).optional(),
    deleted: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    deletedAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    createdAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    updatedAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    organizationId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    staff: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    january: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    february: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    march: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    april: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    may: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    june: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    july: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    august: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    september: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    october: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    november: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    december: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    year: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
