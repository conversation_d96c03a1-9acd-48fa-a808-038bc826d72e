/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { LDocumentKeySchema } from "../enums/LDocumentKey.schema";

type SchemaType = z.ZodType<Prisma.EnumLDocumentKeyFieldUpdateOperationsInput>;
export const EnumLDocumentKeyFieldUpdateOperationsInputObjectSchema: SchemaType =
  z
    .object({
      set: z.lazy(() => LDocumentKeySchema).optional(),
    })
    .strict() as SchemaType;
