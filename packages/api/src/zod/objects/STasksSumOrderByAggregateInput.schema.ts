/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.STasksSumOrderByAggregateInput>;
export const STasksSumOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    lengthInSeconds: z.lazy(() => SortOrderSchema).optional(),
  })
  .strict() as SchemaType;
