/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TBudgetCategoryCountOrderByAggregateInput>;
export const TBudgetCategoryCountOrderByAggregateInputObjectSchema: SchemaType =
  z
    .object({
      id: z.lazy(() => SortOrderSchema).optional(),
      deleted: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      deletedAt: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      createdAt: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      updatedAt: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      organizationId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      category: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      budgetCode: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      parentId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      typeId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      icon: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      whiteIcon: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      isOverhead: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      isFc: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      additionalInfo: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
    })
    .strict() as SchemaType;
