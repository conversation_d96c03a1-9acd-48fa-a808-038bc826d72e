/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TAIConversationMinOrderByAggregateInput>;
export const TAIConversationMinOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    id: z.lazy(() => SortOrderSchema).optional(),
    deleted: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    deletedAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    createdAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    updatedAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    organizationId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    userId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
