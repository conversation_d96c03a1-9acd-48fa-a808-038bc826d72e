/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";

type SchemaType = z.ZodType<Prisma.TMaterialStampCreateManyInput>;
export const TMaterialStampCreateManyInputObjectSchema: SchemaType = z
  .object({
    code: z.union([z.string(), z.null()]).optional().nullable(),
    id: z.string().optional(),
    deleted: z.union([z.boolean(), z.null()]).optional().nullable(),
    deletedAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    createdAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    updatedAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    organizationId: z.string(),
    label: z.union([z.string(), z.null()]).optional().nullable(),
    classification: z.union([z.string(), z.null()]).optional().nullable(),
    category: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    directCostSec: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    referenceUnit: z.union([z.coerce.bigint(), z.null()]).optional().nullable(),
  })
  .strict() as SchemaType;
