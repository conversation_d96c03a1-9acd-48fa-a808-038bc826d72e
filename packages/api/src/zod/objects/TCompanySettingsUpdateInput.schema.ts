/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { NullableBoolFieldUpdateOperationsInputObjectSchema } from "./NullableBoolFieldUpdateOperationsInput.schema";
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from "./NullableDateTimeFieldUpdateOperationsInput.schema";
import { NullableStringFieldUpdateOperationsInputObjectSchema } from "./NullableStringFieldUpdateOperationsInput.schema";
import { OrganizationUpdateOneRequiredWithoutTCompanySettingsNestedInputObjectSchema } from "./OrganizationUpdateOneRequiredWithoutTCompanySettingsNestedInput.schema";
import { StringFieldUpdateOperationsInputObjectSchema } from "./StringFieldUpdateOperationsInput.schema";
import { TFileUpdateOneWithoutCompanyLogosNestedInputObjectSchema } from "./TFileUpdateOneWithoutCompanyLogosNestedInput.schema";

type SchemaType = z.ZodType<Prisma.TCompanySettingsUpdateInput>;
export const TCompanySettingsUpdateInputObjectSchema: SchemaType = z
  .object({
    id: z
      .union([
        z.string(),
        z.lazy(() => StringFieldUpdateOperationsInputObjectSchema),
      ])
      .optional(),
    deleted: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    secretEmailToken: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    currency: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    formatCodeContact: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    formatCodeInvoice: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    formatCodeQuotation: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    formatCodeProject: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    formatCodeGuy: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    formatCodeExpense: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailQuotationSend: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailQuotationFollowUp: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailInvoiceSend: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailInvoiceFollowUp1: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailInvoiceFollowUp2: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailInvoiceFollowUp3: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailInvoiceFollowUp4: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailInvoiceFollowUp5: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailDocumentSend: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    emailSignature: z
      .union([
        z.string(),
        z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    fiscalYearEndDate: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    organization: z
      .lazy(
        () =>
          OrganizationUpdateOneRequiredWithoutTCompanySettingsNestedInputObjectSchema
      )
      .optional(),
    logo: z
      .lazy(() => TFileUpdateOneWithoutCompanyLogosNestedInputObjectSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
