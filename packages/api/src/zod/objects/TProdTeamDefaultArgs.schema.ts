/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TProdTeamIncludeObjectSchema } from "./TProdTeamInclude.schema";
import { TProdTeamSelectObjectSchema } from "./TProdTeamSelect.schema";

type SchemaType = z.ZodType<Prisma.TProdTeamDefaultArgs>;
export const TProdTeamDefaultArgsObjectSchema: SchemaType = z
  .object({
    select: z.lazy(() => TProdTeamSelectObjectSchema).optional(),
    include: z
      .lazy(() => TProdTeamIncludeObjectSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
