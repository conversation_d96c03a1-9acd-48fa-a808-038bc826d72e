/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TCompanyContactCreateWithoutProjectsInputObjectSchema } from "./TCompanyContactCreateWithoutProjectsInput.schema";
import { TCompanyContactWhereUniqueInputObjectSchema } from "./TCompanyContactWhereUniqueInput.schema";

type SchemaType =
  z.ZodType<Prisma.TCompanyContactCreateOrConnectWithoutProjectsInput>;
export const TCompanyContactCreateOrConnectWithoutProjectsInputObjectSchema: SchemaType =
  z
    .object({
      where: z.lazy(() => TCompanyContactWhereUniqueInputObjectSchema),
      create: z.lazy(
        () => TCompanyContactCreateWithoutProjectsInputObjectSchema
      ),
    })
    .strict() as SchemaType;
