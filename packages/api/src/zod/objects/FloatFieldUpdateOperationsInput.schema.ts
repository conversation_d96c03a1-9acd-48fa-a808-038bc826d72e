/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";

type SchemaType = z.ZodType<Prisma.FloatFieldUpdateOperationsInput>;
export const FloatFieldUpdateOperationsInputObjectSchema: SchemaType = z
  .object({
    set: z.coerce.number().optional(),
    increment: z.coerce.number().optional().optional(),
    decrement: z.coerce.number().optional().optional(),
    multiply: z.coerce.number().optional().optional(),
    divide: z.coerce.number().optional().optional(),
  })
  .strict() as SchemaType;
