/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";

type SchemaType = z.ZodType<Prisma.TFileCountOutputTypeSelect>;
export const TFileCountOutputTypeSelectObjectSchema: SchemaType = z
  .object({
    companyLogos: z.boolean().optional(),
    relations: z.boolean().optional().optional(),
  })
  .strict() as SchemaType;
