/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { STasksIncludeObjectSchema } from "./STasksInclude.schema";
import { STasksSelectObjectSchema } from "./STasksSelect.schema";

type SchemaType = z.ZodType<Prisma.STasksDefaultArgs>;
export const STasksDefaultArgsObjectSchema: SchemaType = z
  .object({
    select: z.lazy(() => STasksSelectObjectSchema).optional(),
    include: z
      .lazy(() => STasksIncludeObjectSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
