/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { STasksUpdateWithoutOrganizationInputObjectSchema } from "./STasksUpdateWithoutOrganizationInput.schema";
import { STasksWhereUniqueInputObjectSchema } from "./STasksWhereUniqueInput.schema";

type SchemaType =
  z.ZodType<Prisma.STasksUpdateWithWhereUniqueWithoutOrganizationInput>;
export const STasksUpdateWithWhereUniqueWithoutOrganizationInputObjectSchema: SchemaType =
  z
    .object({
      where: z.lazy(() => STasksWhereUniqueInputObjectSchema),
      data: z.lazy(() => STasksUpdateWithoutOrganizationInputObjectSchema),
    })
    .strict() as SchemaType;
