/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { OrganizationDefaultArgsObjectSchema } from "./OrganizationDefaultArgs.schema";

type SchemaType = z.ZodType<Prisma.TQuotationCompositionSelect>;
export const TQuotationCompositionSelectObjectSchema: SchemaType = z
  .object({
    id: z.boolean().optional(),
    deleted: z.boolean().optional().optional(),
    deletedAt: z.boolean().optional().optional(),
    createdAt: z.boolean().optional().optional(),
    updatedAt: z.boolean().optional().optional(),
    organizationId: z.boolean().optional().optional(),
    organization: z
      .union([z.boolean(), z.lazy(() => OrganizationDefaultArgsObjectSchema)])
      .optional(),
    reference: z.boolean().optional().optional(),
    quotationId: z.boolean().optional().optional(),
    designation: z.boolean().optional().optional(),
    declaredUnitId: z.boolean().optional().optional(),
    unitPrice: z.boolean().optional().optional(),
    vatRate: z.boolean().optional().optional(),
    level: z.boolean().optional().optional(),
    title: z.boolean().optional().optional(),
    articleType: z.boolean().optional().optional(),
    calculatedQuantity: z.boolean().optional().optional(),
    expectedQuality: z.boolean().optional().optional(),
    calculatedUnitId: z.boolean().optional().optional(),
    quantity: z.boolean().optional().optional(),
    option: z.boolean().optional().optional(),
    index: z.boolean().optional().optional(),
    parentId: z.boolean().optional().optional(),
    essential: z.boolean().optional().optional(),
    fallRatio: z.boolean().optional().optional(),
    hourId: z.boolean().optional().optional(),
    materialId: z.boolean().optional().optional(),
    productId: z.boolean().optional().optional(),
    minutes: z.boolean().optional().optional(),
    forcedTitle: z.boolean().optional().optional(),
    stampArticleId: z.boolean().optional().optional(),
    workId: z.boolean().optional().optional(),
    dmIds: z.boolean().optional().optional(),
    deletionLocked: z.boolean().optional().optional(),
    compositionIds: z.boolean().optional().optional(),
  })
  .strict() as SchemaType;
