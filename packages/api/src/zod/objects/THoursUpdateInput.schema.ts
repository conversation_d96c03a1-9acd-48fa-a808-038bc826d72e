/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { NullableBigIntFieldUpdateOperationsInputObjectSchema } from "./NullableBigIntFieldUpdateOperationsInput.schema";
import { NullableBoolFieldUpdateOperationsInputObjectSchema } from "./NullableBoolFieldUpdateOperationsInput.schema";
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from "./NullableDateTimeFieldUpdateOperationsInput.schema";
import { NullableFloatFieldUpdateOperationsInputObjectSchema } from "./NullableFloatFieldUpdateOperationsInput.schema";
import { NullableIntFieldUpdateOperationsInputObjectSchema } from "./NullableIntFieldUpdateOperationsInput.schema";
import { OrganizationUpdateOneRequiredWithoutTHoursNestedInputObjectSchema } from "./OrganizationUpdateOneRequiredWithoutTHoursNestedInput.schema";
import { StringFieldUpdateOperationsInputObjectSchema } from "./StringFieldUpdateOperationsInput.schema";

type SchemaType = z.ZodType<Prisma.THoursUpdateInput>;
export const THoursUpdateInputObjectSchema: SchemaType = z
  .object({
    id: z
      .union([
        z.string(),
        z.lazy(() => StringFieldUpdateOperationsInputObjectSchema),
      ])
      .optional(),
    deleted: z
      .union([
        z.boolean(),
        z.lazy(() => NullableBoolFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    staff: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    site: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    year: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    week: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    monday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    tuesday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    wednesday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    thursday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    friday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    saturday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    sunday: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    basket: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    overtime_100: z
      .union([
        z.coerce.number(),
        z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    month: z
      .union([
        z.coerce.bigint(),
        z.lazy(() => NullableBigIntFieldUpdateOperationsInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    organization: z
      .lazy(
        () => OrganizationUpdateOneRequiredWithoutTHoursNestedInputObjectSchema
      )
      .optional(),
  })
  .strict() as SchemaType;
