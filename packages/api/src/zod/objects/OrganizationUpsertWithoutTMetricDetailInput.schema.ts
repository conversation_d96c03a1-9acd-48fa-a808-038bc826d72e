/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { OrganizationCreateWithoutTMetricDetailInputObjectSchema } from "./OrganizationCreateWithoutTMetricDetailInput.schema";
import { OrganizationUpdateWithoutTMetricDetailInputObjectSchema } from "./OrganizationUpdateWithoutTMetricDetailInput.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";

type SchemaType = z.ZodType<Prisma.OrganizationUpsertWithoutTMetricDetailInput>;
export const OrganizationUpsertWithoutTMetricDetailInputObjectSchema: SchemaType =
  z
    .object({
      update: z.lazy(
        () => OrganizationUpdateWithoutTMetricDetailInputObjectSchema
      ),
      create: z.lazy(
        () => OrganizationCreateWithoutTMetricDetailInputObjectSchema
      ),
      where: z.lazy(() => OrganizationWhereInputObjectSchema).optional(),
    })
    .strict() as SchemaType;
