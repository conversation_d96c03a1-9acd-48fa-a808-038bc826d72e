/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";
import { TTeamStatsOrderByRelevanceFieldEnumSchema } from "../enums/TTeamStatsOrderByRelevanceFieldEnum.schema";

type SchemaType = z.ZodType<Prisma.TTeamStatsOrderByRelevanceInput>;
export const TTeamStatsOrderByRelevanceInputObjectSchema: SchemaType = z
  .object({
    fields: z.union([
      z.lazy(() => TTeamStatsOrderByRelevanceFieldEnumSchema),
      z.lazy(() => TTeamStatsOrderByRelevanceFieldEnumSchema).array(),
    ]),
    sort: z.lazy(() => SortOrderSchema),
    search: z.string(),
  })
  .strict() as SchemaType;
