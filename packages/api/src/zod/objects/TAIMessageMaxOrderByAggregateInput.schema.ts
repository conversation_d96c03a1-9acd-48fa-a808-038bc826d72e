/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TAIMessageMaxOrderByAggregateInput>;
export const TAIMessageMaxOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    id: z.lazy(() => SortOrderSchema).optional(),
    deleted: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    deletedAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    createdAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    updatedAt: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    organizationId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    userId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    author: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    conversationId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    content: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
