/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TProjectCreateWithoutExpensesInputObjectSchema } from "./TProjectCreateWithoutExpensesInput.schema";
import { TProjectUpdateWithoutExpensesInputObjectSchema } from "./TProjectUpdateWithoutExpensesInput.schema";
import { TProjectWhereInputObjectSchema } from "./TProjectWhereInput.schema";

type SchemaType = z.ZodType<Prisma.TProjectUpsertWithoutExpensesInput>;
export const TProjectUpsertWithoutExpensesInputObjectSchema: SchemaType = z
  .object({
    update: z.lazy(() => TProjectUpdateWithoutExpensesInputObjectSchema),
    create: z.lazy(() => TProjectCreateWithoutExpensesInputObjectSchema),
    where: z.lazy(() => TProjectWhereInputObjectSchema).optional(),
  })
  .strict() as SchemaType;
