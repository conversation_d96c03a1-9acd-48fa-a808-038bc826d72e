/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { OrganizationUpdateWithoutTCompanyContactInputObjectSchema } from "./OrganizationUpdateWithoutTCompanyContactInput.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";

type SchemaType =
  z.ZodType<Prisma.OrganizationUpdateToOneWithWhereWithoutTCompanyContactInput>;
export const OrganizationUpdateToOneWithWhereWithoutTCompanyContactInputObjectSchema: SchemaType =
  z
    .object({
      where: z.lazy(() => OrganizationWhereInputObjectSchema).optional(),
      data: z.lazy(
        () => OrganizationUpdateWithoutTCompanyContactInputObjectSchema
      ),
    })
    .strict() as SchemaType;
