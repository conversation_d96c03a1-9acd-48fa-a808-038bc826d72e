/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { OrganizationCreateWithoutTAIMessageInputObjectSchema } from "./OrganizationCreateWithoutTAIMessageInput.schema";
import { OrganizationUpdateWithoutTAIMessageInputObjectSchema } from "./OrganizationUpdateWithoutTAIMessageInput.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";

type SchemaType = z.ZodType<Prisma.OrganizationUpsertWithoutTAIMessageInput>;
export const OrganizationUpsertWithoutTAIMessageInputObjectSchema: SchemaType =
  z
    .object({
      update: z.lazy(
        () => OrganizationUpdateWithoutTAIMessageInputObjectSchema
      ),
      create: z.lazy(
        () => OrganizationCreateWithoutTAIMessageInputObjectSchema
      ),
      where: z.lazy(() => OrganizationWhereInputObjectSchema).optional(),
    })
    .strict() as SchemaType;
