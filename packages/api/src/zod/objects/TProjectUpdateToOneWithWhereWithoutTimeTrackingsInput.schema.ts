/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TProjectUpdateWithoutTimeTrackingsInputObjectSchema } from "./TProjectUpdateWithoutTimeTrackingsInput.schema";
import { TProjectWhereInputObjectSchema } from "./TProjectWhereInput.schema";

type SchemaType =
  z.ZodType<Prisma.TProjectUpdateToOneWithWhereWithoutTimeTrackingsInput>;
export const TProjectUpdateToOneWithWhereWithoutTimeTrackingsInputObjectSchema: SchemaType =
  z
    .object({
      where: z.lazy(() => TProjectWhereInputObjectSchema).optional(),
      data: z.lazy(() => TProjectUpdateWithoutTimeTrackingsInputObjectSchema),
    })
    .strict() as SchemaType;
