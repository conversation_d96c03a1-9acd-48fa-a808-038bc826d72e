/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { NullableJsonNullValueInputSchema } from "../enums/NullableJsonNullValueInput.schema";
import { TSitePurchaseOrdersCreatebaseQuotesListInputObjectSchema } from "./TSitePurchaseOrdersCreatebaseQuotesListInput.schema";
import { TSitePurchaseOrdersCreatecompoIdsInputObjectSchema } from "./TSitePurchaseOrdersCreatecompoIdsInput.schema";
import { TSitePurchaseOrdersCreateemailIdsInputObjectSchema } from "./TSitePurchaseOrdersCreateemailIdsInput.schema";
import { TSitePurchaseOrdersCreateexecutionMonthsInputObjectSchema } from "./TSitePurchaseOrdersCreateexecutionMonthsInput.schema";
import { TSitePurchaseOrdersCreateguysListInputObjectSchema } from "./TSitePurchaseOrdersCreateguysListInput.schema";

const literalSchema = z.union([z.string(), z.coerce.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    literalSchema,
    z.array(jsonSchema.nullable()),
    z.record(jsonSchema.nullable()),
  ])
);

type SchemaType = z.ZodType<Prisma.TSitePurchaseOrdersCreateManyInput>;
export const TSitePurchaseOrdersCreateManyInputObjectSchema: SchemaType = z
  .object({
    code: z.union([z.string(), z.null()]).optional().nullable(),
    version: z.union([z.string(), z.null()]).optional().nullable(),
    id: z.string().optional(),
    deleted: z.union([z.boolean(), z.null()]).optional().nullable(),
    deletedAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    createdAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    updatedAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    organizationId: z.string(),
    creatorId: z.union([z.string(), z.null()]).optional().nullable(),
    projectId: z.union([z.string(), z.null()]).optional().nullable(),
    contactId: z.union([z.string(), z.null()]).optional().nullable(),
    amountExclVatBis: z
      .union([z.coerce.bigint(), z.null()])
      .optional()
      .nullable(),
    grossAmount: z.union([z.coerce.bigint(), z.null()]).optional().nullable(),
    directPayment: z.union([z.boolean(), z.null()]).optional().nullable(),
    comment: z.union([z.string(), z.null()]).optional().nullable(),
    vatSelfLiquidation: z.union([z.boolean(), z.null()]).optional().nullable(),
    warrantyRetention: z.union([z.boolean(), z.null()]).optional().nullable(),
    supplyIncluded: z.union([z.boolean(), z.null()]).optional().nullable(),
    paymentTermId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    relatedChargeId: z
      .union([z.coerce.number(), z.null()])
      .optional()
      .nullable(),
    centerAlignment: z.union([z.boolean(), z.null()]).optional().nullable(),
    orderRejected: z.union([z.boolean(), z.null()]).optional().nullable(),
    label: z.union([z.string(), z.null()]).optional().nullable(),
    originId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    hasProgress: z.union([z.boolean(), z.null()]).optional().nullable(),
    amountExclVat: z.union([z.coerce.bigint(), z.null()]).optional().nullable(),
    vatAmount: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    externalReference: z.union([z.string(), z.null()]).optional().nullable(),
    revisionIndexId: z
      .union([z.coerce.number(), z.null()])
      .optional()
      .nullable(),
    indexRevision: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    orderTypeId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    siteAddress: z.union([z.string(), z.null()]).optional().nullable(),
    staff: z.union([z.boolean(), z.null()]).optional().nullable(),
    plans: z.union([z.boolean(), z.null()]).optional().nullable(),
    meansAccess: z.union([z.boolean(), z.null()]).optional().nullable(),
    material: z.union([z.boolean(), z.null()]).optional().nullable(),
    consumables: z.union([z.boolean(), z.null()]).optional().nullable(),
    meetings: z.union([z.boolean(), z.null()]).optional().nullable(),
    delivery: z.union([z.boolean(), z.null()]).optional().nullable(),
    guysList: z
      .union([
        z.lazy(() => TSitePurchaseOrdersCreateguysListInputObjectSchema),
        z.coerce.bigint().array(),
      ])
      .optional(),
    baseQuotesList: z
      .union([
        z.lazy(() => TSitePurchaseOrdersCreatebaseQuotesListInputObjectSchema),
        z.coerce.bigint().array(),
      ])
      .optional(),
    executionMonths: z
      .union([
        z.lazy(() => TSitePurchaseOrdersCreateexecutionMonthsInputObjectSchema),
        z.coerce.bigint().array(),
      ])
      .optional(),
    documentDate: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    endDate: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    startDate: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    signatureDate: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    sendingDate: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    chargesJson: z
      .union([z.lazy(() => NullableJsonNullValueInputSchema), jsonSchema])
      .optional(),
    emailIds: z
      .union([
        z.lazy(() => TSitePurchaseOrdersCreateemailIdsInputObjectSchema),
        z.coerce.number().array(),
      ])
      .optional(),
    compoIds: z
      .union([
        z.lazy(() => TSitePurchaseOrdersCreatecompoIdsInputObjectSchema),
        z.coerce.number().array(),
      ])
      .optional(),
  })
  .strict() as SchemaType;
