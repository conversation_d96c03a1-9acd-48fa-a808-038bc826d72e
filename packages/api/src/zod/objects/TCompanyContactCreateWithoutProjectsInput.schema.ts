/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { OrganizationCreateNestedOneWithoutTCompanyContactInputObjectSchema } from "./OrganizationCreateNestedOneWithoutTCompanyContactInput.schema";
import { TCompanyContactCreatecollectiveAgreementsInputObjectSchema } from "./TCompanyContactCreatecollectiveAgreementsInput.schema";
import { TCompanyContactCreatepreferredBudgetsIdsInputObjectSchema } from "./TCompanyContactCreatepreferredBudgetsIdsInput.schema";
import { TCompanyContactCreateskillsInputObjectSchema } from "./TCompanyContactCreateskillsInput.schema";
import { TContactCreateNestedOneWithoutCompany_contactInputObjectSchema } from "./TContactCreateNestedOneWithoutCompany_contactInput.schema";
import { TExpenseCreateNestedManyWithoutCompanyContactInputObjectSchema } from "./TExpenseCreateNestedManyWithoutCompanyContactInput.schema";
import { TGuyCreateNestedManyWithoutContactInputObjectSchema } from "./TGuyCreateNestedManyWithoutContactInput.schema";
import { TQuotationCreateNestedManyWithoutCompanyContactInputObjectSchema } from "./TQuotationCreateNestedManyWithoutCompanyContactInput.schema";

type SchemaType = z.ZodType<Prisma.TCompanyContactCreateWithoutProjectsInput>;
export const TCompanyContactCreateWithoutProjectsInputObjectSchema: SchemaType =
  z
    .object({
      address: z.union([z.string(), z.null()]).optional().nullable(),
      postalCode: z.union([z.string(), z.null()]).optional().nullable(),
      city: z.union([z.string(), z.null()]).optional().nullable(),
      countryId: z.union([z.string(), z.null()]).optional().nullable(),
      code: z.union([z.string(), z.null()]).optional().nullable(),
      id: z.string().optional(),
      createdAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      updatedAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      name: z.union([z.string(), z.null()]).optional().nullable(),
      firstName: z.union([z.string(), z.null()]).optional().nullable(),
      lastName: z.union([z.string(), z.null()]).optional().nullable(),
      title: z.union([z.string(), z.null()]).optional().nullable(),
      phone: z.union([z.string(), z.null()]).optional().nullable(),
      website: z.union([z.string(), z.null()]).optional().nullable(),
      email: z.union([z.string(), z.null()]).optional().nullable(),
      sirenNumber: z.union([z.string(), z.null()]).optional().nullable(),
      siretNumber: z.union([z.string(), z.null()]).optional().nullable(),
      comment: z.union([z.string(), z.null()]).optional().nullable(),
      creator: z.union([z.string(), z.null()]).optional().nullable(),
      vatRate: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      vatNumber: z.union([z.string(), z.null()]).optional().nullable(),
      capital: z.union([z.coerce.bigint(), z.null()]).optional().nullable(),
      legalForm: z.union([z.string(), z.null()]).optional().nullable(),
      legalFormId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      codeNaf: z.union([z.string(), z.null()]).optional().nullable(),
      accountingCode: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      recipient: z.union([z.boolean(), z.null()]).optional().nullable(),
      company: z.union([z.string(), z.null()]).optional().nullable(),
      insurancePolicy: z.union([z.string(), z.null()]).optional().nullable(),
      linkedContactId: z.union([z.string(), z.null()]).optional().nullable(),
      familyId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      salesAccountCodeId: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      skills: z
        .union([
          z.lazy(() => TCompanyContactCreateskillsInputObjectSchema),
          z.coerce.number().array(),
        ])
        .optional(),
      phoneCode: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      doNotContact: z.union([z.boolean(), z.null()]).optional().nullable(),
      registrationDateRne: z
        .union([z.string(), z.null()])
        .optional()
        .nullable(),
      registrationDateRcs: z
        .union([z.string(), z.null()])
        .optional()
        .nullable(),
      creationDate: z.union([z.string(), z.null()]).optional().nullable(),
      creationDateTz: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      lastModifiedDateTz: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      preferredBudgetsIds: z
        .union([
          z.lazy(
            () => TCompanyContactCreatepreferredBudgetsIdsInputObjectSchema
          ),
          z.coerce.number().array(),
        ])
        .optional(),
      collectiveAgreements: z
        .union([
          z.lazy(
            () => TCompanyContactCreatecollectiveAgreementsInputObjectSchema
          ),
          z.coerce.number().array(),
        ])
        .optional(),
      isMe: z.union([z.boolean(), z.null()]).optional().nullable(),
      inSstList: z.boolean().optional().optional(),
      inClientList: z.boolean().optional().optional(),
      inSupplierList: z.boolean().optional().optional(),
      inFgProvidersList: z.boolean().optional().optional(),
      inSiteProvidersList: z.boolean().optional().optional(),
      isTempAgency: z.boolean().optional().optional(),
      inSupervisorsList: z.boolean().optional().optional(),
      inSyndicsList: z.boolean().optional().optional(),
      inGroupCompaniesList: z
        .union([z.boolean(), z.null()])
        .optional()
        .nullable(),
      organization: z.lazy(
        () => OrganizationCreateNestedOneWithoutTCompanyContactInputObjectSchema
      ),
      guys: z
        .lazy(() => TGuyCreateNestedManyWithoutContactInputObjectSchema)
        .optional()
        .optional(),
      contact: z
        .lazy(
          () => TContactCreateNestedOneWithoutCompany_contactInputObjectSchema
        )
        .optional()
        .optional(),
      expenses: z
        .lazy(
          () => TExpenseCreateNestedManyWithoutCompanyContactInputObjectSchema
        )
        .optional()
        .optional(),
      quotations: z
        .lazy(
          () => TQuotationCreateNestedManyWithoutCompanyContactInputObjectSchema
        )
        .optional()
        .optional(),
    })
    .strict() as SchemaType;
