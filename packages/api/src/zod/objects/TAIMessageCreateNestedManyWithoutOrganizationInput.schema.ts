/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TAIMessageCreateManyOrganizationInputEnvelopeObjectSchema } from "./TAIMessageCreateManyOrganizationInputEnvelope.schema";
import { TAIMessageCreateOrConnectWithoutOrganizationInputObjectSchema } from "./TAIMessageCreateOrConnectWithoutOrganizationInput.schema";
import { TAIMessageCreateWithoutOrganizationInputObjectSchema } from "./TAIMessageCreateWithoutOrganizationInput.schema";
import { TAIMessageWhereUniqueInputObjectSchema } from "./TAIMessageWhereUniqueInput.schema";

type SchemaType =
  z.ZodType<Prisma.TAIMessageCreateNestedManyWithoutOrganizationInput>;
export const TAIMessageCreateNestedManyWithoutOrganizationInputObjectSchema: SchemaType =
  z
    .object({
      create: z
        .union([
          z.lazy(() => TAIMessageCreateWithoutOrganizationInputObjectSchema),
          z
            .lazy(() => TAIMessageCreateWithoutOrganizationInputObjectSchema)
            .array(),
        ])
        .optional(),
      connectOrCreate: z
        .union([
          z.lazy(
            () => TAIMessageCreateOrConnectWithoutOrganizationInputObjectSchema
          ),
          z
            .lazy(
              () =>
                TAIMessageCreateOrConnectWithoutOrganizationInputObjectSchema
            )
            .array(),
        ])
        .optional(),
      createMany: z
        .lazy(() => TAIMessageCreateManyOrganizationInputEnvelopeObjectSchema)
        .optional(),
      connect: z
        .union([
          z.lazy(() => TAIMessageWhereUniqueInputObjectSchema),
          z.lazy(() => TAIMessageWhereUniqueInputObjectSchema).array(),
        ])
        .optional(),
    })
    .strict() as SchemaType;
