/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TGenericProductStampWhereInputObjectSchema } from "./TGenericProductStampWhereInput.schema";

type SchemaType = z.ZodType<Prisma.TGenericProductStampListRelationFilter>;
export const TGenericProductStampListRelationFilterObjectSchema: SchemaType = z
  .object({
    every: z.lazy(() => TGenericProductStampWhereInputObjectSchema).optional(),
    some: z
      .lazy(() => TGenericProductStampWhereInputObjectSchema)
      .optional()
      .optional(),
    none: z
      .lazy(() => TGenericProductStampWhereInputObjectSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
