/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";

type SchemaType = z.ZodType<Prisma.TFileRelationMaxAggregateInputType>;
export const TFileRelationMaxAggregateInputObjectSchema: SchemaType = z
  .object({
    createdAt: z.literal(true).optional(),
    updatedAt: z.literal(true).optional().optional(),
    linkedId: z.literal(true).optional().optional(),
    linkedModel: z.literal(true).optional().optional(),
    id: z.literal(true).optional().optional(),
    fileId: z.literal(true).optional().optional(),
  })
  .strict() as SchemaType;
