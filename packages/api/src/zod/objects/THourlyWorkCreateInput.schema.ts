/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { OrganizationCreateNestedOneWithoutTHourlyWorkInputObjectSchema } from "./OrganizationCreateNestedOneWithoutTHourlyWorkInput.schema";

type SchemaType = z.ZodType<Prisma.THourlyWorkCreateInput>;
export const THourlyWorkCreateInputObjectSchema: SchemaType = z
  .object({
    id: z.string().optional(),
    deleted: z.union([z.boolean(), z.null()]).optional().nullable(),
    deletedAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    createdAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    updatedAt: z
      .union([z.union([z.date(), z.string().datetime().optional()]), z.null()])
      .optional()
      .nullable(),
    reference: z.union([z.string(), z.null()]).optional().nullable(),
    label: z.union([z.string(), z.null()]).optional().nullable(),
    directCost: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    overheads: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    costPrice: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    margin: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    salesUnit: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    salesPrice: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    time: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    quantity: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    workCode: z.union([z.coerce.number(), z.null()]).optional().nullable(),
    organization: z.lazy(
      () => OrganizationCreateNestedOneWithoutTHourlyWorkInputObjectSchema
    ),
  })
  .strict() as SchemaType;
