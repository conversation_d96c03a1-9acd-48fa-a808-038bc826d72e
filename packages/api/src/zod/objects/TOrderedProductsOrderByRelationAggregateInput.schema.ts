/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType =
  z.ZodType<Prisma.TOrderedProductsOrderByRelationAggregateInput>;
export const TOrderedProductsOrderByRelationAggregateInputObjectSchema: SchemaType =
  z
    .object({
      _count: z.lazy(() => SortOrderSchema).optional(),
    })
    .strict() as SchemaType;
