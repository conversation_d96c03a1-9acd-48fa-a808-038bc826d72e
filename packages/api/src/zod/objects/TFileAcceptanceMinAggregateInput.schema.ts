/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";

type SchemaType = z.ZodType<Prisma.TFileAcceptanceMinAggregateInputType>;
export const TFileAcceptanceMinAggregateInputObjectSchema: SchemaType = z
  .object({
    id: z.literal(true).optional(),
    deleted: z.literal(true).optional().optional(),
    deletedAt: z.literal(true).optional().optional(),
    createdAt: z.literal(true).optional().optional(),
    updatedAt: z.literal(true).optional().optional(),
    acceptedAt: z.literal(true).optional().optional(),
    fileId: z.literal(true).optional().optional(),
    userId: z.literal(true).optional().optional(),
  })
  .strict() as SchemaType;
