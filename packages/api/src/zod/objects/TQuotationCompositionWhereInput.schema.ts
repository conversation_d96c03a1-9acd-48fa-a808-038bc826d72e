/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BoolNullableFilterObjectSchema } from "./BoolNullableFilter.schema";
import { DateTimeNullableFilterObjectSchema } from "./DateTimeNullableFilter.schema";
import { FloatNullableFilterObjectSchema } from "./FloatNullableFilter.schema";
import { IntNullableFilterObjectSchema } from "./IntNullableFilter.schema";
import { IntNullableListFilterObjectSchema } from "./IntNullableListFilter.schema";
import { OrganizationScalarRelationFilterObjectSchema } from "./OrganizationScalarRelationFilter.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";
import { StringFilterObjectSchema } from "./StringFilter.schema";
import { StringNullableFilterObjectSchema } from "./StringNullableFilter.schema";

type SchemaType = z.ZodType<Prisma.TQuotationCompositionWhereInput>;
export const TQuotationCompositionWhereInputObjectSchema: SchemaType = z
  .object({
    AND: z
      .union([
        z.lazy(() => TQuotationCompositionWhereInputObjectSchema),
        z.lazy(() => TQuotationCompositionWhereInputObjectSchema).array(),
      ])
      .optional(),
    OR: z
      .lazy(() => TQuotationCompositionWhereInputObjectSchema)
      .array()
      .optional(),
    NOT: z
      .union([
        z.lazy(() => TQuotationCompositionWhereInputObjectSchema),
        z.lazy(() => TQuotationCompositionWhereInputObjectSchema).array(),
      ])
      .optional(),
    id: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    deleted: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    organizationId: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    reference: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    quotationId: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    designation: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    declaredUnitId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    unitPrice: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    vatRate: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    level: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    title: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    articleType: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    calculatedQuantity: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    expectedQuality: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    calculatedUnitId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    quantity: z
      .union([
        z.lazy(() => FloatNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    option: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    index: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    parentId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    essential: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    fallRatio: z
      .union([
        z.lazy(() => FloatNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    hourId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    materialId: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    productId: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    minutes: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    forcedTitle: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    stampArticleId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    workId: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    dmIds: z
      .lazy(() => IntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    deletionLocked: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    compositionIds: z
      .lazy(() => IntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    organization: z
      .union([
        z.lazy(() => OrganizationScalarRelationFilterObjectSchema),
        z.lazy(() => OrganizationWhereInputObjectSchema),
      ])
      .optional(),
  })
  .strict() as SchemaType;
