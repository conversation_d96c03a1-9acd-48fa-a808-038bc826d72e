/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BigIntNullableListFilterObjectSchema } from "./BigIntNullableListFilter.schema";
import { BigIntNullableWithAggregatesFilterObjectSchema } from "./BigIntNullableWithAggregatesFilter.schema";
import { BoolNullableWithAggregatesFilterObjectSchema } from "./BoolNullableWithAggregatesFilter.schema";
import { DateTimeNullableWithAggregatesFilterObjectSchema } from "./DateTimeNullableWithAggregatesFilter.schema";
import { IntNullableListFilterObjectSchema } from "./IntNullableListFilter.schema";
import { IntNullableWithAggregatesFilterObjectSchema } from "./IntNullableWithAggregatesFilter.schema";
import { JsonNullableWithAggregatesFilterObjectSchema } from "./JsonNullableWithAggregatesFilter.schema";
import { StringNullableListFilterObjectSchema } from "./StringNullableListFilter.schema";
import { StringNullableWithAggregatesFilterObjectSchema } from "./StringNullableWithAggregatesFilter.schema";
import { StringWithAggregatesFilterObjectSchema } from "./StringWithAggregatesFilter.schema";

type SchemaType = z.ZodType<Prisma.TOrderScalarWhereWithAggregatesInput>;
export const TOrderScalarWhereWithAggregatesInputObjectSchema: SchemaType = z
  .object({
    AND: z
      .union([
        z.lazy(() => TOrderScalarWhereWithAggregatesInputObjectSchema),
        z.lazy(() => TOrderScalarWhereWithAggregatesInputObjectSchema).array(),
      ])
      .optional(),
    OR: z
      .lazy(() => TOrderScalarWhereWithAggregatesInputObjectSchema)
      .array()
      .optional(),
    NOT: z
      .union([
        z.lazy(() => TOrderScalarWhereWithAggregatesInputObjectSchema),
        z.lazy(() => TOrderScalarWhereWithAggregatesInputObjectSchema).array(),
      ])
      .optional(),
    code: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    version: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    id: z
      .union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()])
      .optional(),
    deleted: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    organizationId: z
      .union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()])
      .optional(),
    creatorId: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    projectId: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    contactId: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    amountExclTaxBis: z
      .union([
        z.lazy(() => BigIntNullableWithAggregatesFilterObjectSchema),
        z.coerce.bigint(),
        z.null(),
      ])
      .optional()
      .nullable(),
    amountInclTax: z
      .union([
        z.lazy(() => BigIntNullableWithAggregatesFilterObjectSchema),
        z.coerce.bigint(),
        z.null(),
      ])
      .optional()
      .nullable(),
    directPayment: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    comment: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    vatSelfLiquidation: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    warrantyRetention: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    supplyIncluded: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    paymentTermId: z
      .union([
        z.lazy(() => IntNullableWithAggregatesFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    relatedChargeId: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    centerAlignment: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    orderRejected: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    label: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    originId: z
      .union([
        z.lazy(() => IntNullableWithAggregatesFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    hasProgress: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    amountExclTax: z
      .union([
        z.lazy(() => BigIntNullableWithAggregatesFilterObjectSchema),
        z.coerce.bigint(),
        z.null(),
      ])
      .optional()
      .nullable(),
    vatAmount: z
      .union([
        z.lazy(() => IntNullableWithAggregatesFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    externalReference: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    revisionIndexId: z
      .union([
        z.lazy(() => IntNullableWithAggregatesFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    indexRevision: z
      .union([
        z.lazy(() => IntNullableWithAggregatesFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    orderTypeId: z
      .union([
        z.lazy(() => IntNullableWithAggregatesFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    siteAddress: z
      .union([
        z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    staff: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    plans: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    accessMeans: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    material: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    consumables: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    meetings: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    delivery: z
      .union([
        z.lazy(() => BoolNullableWithAggregatesFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    guyList: z
      .lazy(() => BigIntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    baseQuoteList: z
      .lazy(() => BigIntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    executionMonths: z
      .lazy(() => BigIntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    documentDateTz: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    endDateTz: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    startDateTz: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    signatureDateTz: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    sendDateTz: z
      .union([
        z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    chargesJson: z
      .lazy(() => JsonNullableWithAggregatesFilterObjectSchema)
      .optional()
      .optional(),
    emailIds: z
      .lazy(() => StringNullableListFilterObjectSchema)
      .optional()
      .optional(),
    componentIds: z
      .lazy(() => IntNullableListFilterObjectSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
