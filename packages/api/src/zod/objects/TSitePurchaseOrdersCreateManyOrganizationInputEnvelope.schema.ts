/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TSitePurchaseOrdersCreateManyOrganizationInputObjectSchema } from "./TSitePurchaseOrdersCreateManyOrganizationInput.schema";

type SchemaType =
  z.ZodType<Prisma.TSitePurchaseOrdersCreateManyOrganizationInputEnvelope>;
export const TSitePurchaseOrdersCreateManyOrganizationInputEnvelopeObjectSchema: SchemaType =
  z
    .object({
      data: z.union([
        z.lazy(
          () => TSitePurchaseOrdersCreateManyOrganizationInputObjectSchema
        ),
        z
          .lazy(
            () => TSitePurchaseOrdersCreateManyOrganizationInputObjectSchema
          )
          .array(),
      ]),
      skipDuplicates: z.boolean().optional(),
    })
    .strict() as SchemaType;
