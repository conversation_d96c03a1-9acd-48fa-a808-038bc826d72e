/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TSupplierAvgOrderByAggregateInput>;
export const TSupplierAvgOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    accountingCodeId: z.lazy(() => SortOrderSchema).optional(),
    vatId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
