/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BigIntNullableFilterObjectSchema } from "./BigIntNullableFilter.schema";
import { BoolFilterObjectSchema } from "./BoolFilter.schema";
import { BoolNullableFilterObjectSchema } from "./BoolNullableFilter.schema";
import { DateTimeNullableFilterObjectSchema } from "./DateTimeNullableFilter.schema";
import { IntNullableFilterObjectSchema } from "./IntNullableFilter.schema";
import { IntNullableListFilterObjectSchema } from "./IntNullableListFilter.schema";
import { OrganizationScalarRelationFilterObjectSchema } from "./OrganizationScalarRelationFilter.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";
import { StringFilterObjectSchema } from "./StringFilter.schema";
import { StringNullableFilterObjectSchema } from "./StringNullableFilter.schema";
import { TContactNullableScalarRelationFilterObjectSchema } from "./TContactNullableScalarRelationFilter.schema";
import { TContactWhereInputObjectSchema } from "./TContactWhereInput.schema";
import { TExpenseListRelationFilterObjectSchema } from "./TExpenseListRelationFilter.schema";
import { TGuyListRelationFilterObjectSchema } from "./TGuyListRelationFilter.schema";
import { TProjectListRelationFilterObjectSchema } from "./TProjectListRelationFilter.schema";
import { TQuotationListRelationFilterObjectSchema } from "./TQuotationListRelationFilter.schema";

type SchemaType = z.ZodType<Prisma.TCompanyContactWhereInput>;
export const TCompanyContactWhereInputObjectSchema: SchemaType = z
  .object({
    AND: z
      .union([
        z.lazy(() => TCompanyContactWhereInputObjectSchema),
        z.lazy(() => TCompanyContactWhereInputObjectSchema).array(),
      ])
      .optional(),
    OR: z
      .lazy(() => TCompanyContactWhereInputObjectSchema)
      .array()
      .optional(),
    NOT: z
      .union([
        z.lazy(() => TCompanyContactWhereInputObjectSchema),
        z.lazy(() => TCompanyContactWhereInputObjectSchema).array(),
      ])
      .optional(),
    organizationId: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    address: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    postalCode: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    city: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    countryId: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    code: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    id: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    createdAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    name: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    firstName: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    lastName: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    title: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    phone: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    website: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    email: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    sirenNumber: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    siretNumber: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    comment: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    creator: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    vatRate: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    vatNumber: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    capital: z
      .union([
        z.lazy(() => BigIntNullableFilterObjectSchema),
        z.coerce.bigint(),
        z.null(),
      ])
      .optional()
      .nullable(),
    legalForm: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    legalFormId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    codeNaf: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    accountingCode: z
      .union([
        z.lazy(() => BigIntNullableFilterObjectSchema),
        z.coerce.bigint(),
        z.null(),
      ])
      .optional()
      .nullable(),
    recipient: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    company: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    insurancePolicy: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    linkedContactId: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    familyId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    salesAccountCodeId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    skills: z
      .lazy(() => IntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    phoneCode: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    doNotContact: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    registrationDateRne: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    registrationDateRcs: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    creationDate: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    creationDateTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    lastModifiedDateTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    preferredBudgetsIds: z
      .lazy(() => IntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    collectiveAgreements: z
      .lazy(() => IntNullableListFilterObjectSchema)
      .optional()
      .optional(),
    contactId: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    isMe: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    inSstList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inClientList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inSupplierList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inFgProvidersList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inSiteProvidersList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    isTempAgency: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inSupervisorsList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inSyndicsList: z
      .union([z.lazy(() => BoolFilterObjectSchema), z.boolean()])
      .optional(),
    inGroupCompaniesList: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    organization: z
      .union([
        z.lazy(() => OrganizationScalarRelationFilterObjectSchema),
        z.lazy(() => OrganizationWhereInputObjectSchema),
      ])
      .optional(),
    guys: z
      .lazy(() => TGuyListRelationFilterObjectSchema)
      .optional()
      .optional(),
    contact: z
      .union([
        z.lazy(() => TContactNullableScalarRelationFilterObjectSchema),
        z.lazy(() => TContactWhereInputObjectSchema),
        z.null(),
      ])
      .optional()
      .nullable(),
    expenses: z
      .lazy(() => TExpenseListRelationFilterObjectSchema)
      .optional()
      .optional(),
    quotations: z
      .lazy(() => TQuotationListRelationFilterObjectSchema)
      .optional()
      .optional(),
    projects: z
      .lazy(() => TProjectListRelationFilterObjectSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
