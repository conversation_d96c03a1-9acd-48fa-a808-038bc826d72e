/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BoolNullableFilterObjectSchema } from "./BoolNullableFilter.schema";
import { DateTimeNullableFilterObjectSchema } from "./DateTimeNullableFilter.schema";
import { IntNullableFilterObjectSchema } from "./IntNullableFilter.schema";
import { OrganizationScalarRelationFilterObjectSchema } from "./OrganizationScalarRelationFilter.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";
import { StringFilterObjectSchema } from "./StringFilter.schema";
import { StringNullableFilterObjectSchema } from "./StringNullableFilter.schema";

type SchemaType = z.ZodType<Prisma.THourStampWhereInput>;
export const THourStampWhereInputObjectSchema: SchemaType = z
  .object({
    AND: z
      .union([
        z.lazy(() => THourStampWhereInputObjectSchema),
        z.lazy(() => THourStampWhereInputObjectSchema).array(),
      ])
      .optional(),
    OR: z
      .lazy(() => THourStampWhereInputObjectSchema)
      .array()
      .optional(),
    NOT: z
      .union([
        z.lazy(() => THourStampWhereInputObjectSchema),
        z.lazy(() => THourStampWhereInputObjectSchema).array(),
      ])
      .optional(),
    id: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    deleted: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    organizationId: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    reference: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    label: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    salesUnit: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    library: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    directCostSec: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    durationInMinutes: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    unitId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    organization: z
      .union([
        z.lazy(() => OrganizationScalarRelationFilterObjectSchema),
        z.lazy(() => OrganizationWhereInputObjectSchema),
      ])
      .optional(),
  })
  .strict() as SchemaType;
