/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from "zod";

const baseSchema = z
  .object({
    id: z.string(),
    deleted: z.boolean().default(false).nullish(),
    deletedAt: z.coerce.date().nullish(),
    createdAt: z.coerce
      .date()
      .default(() => new Date())
      .nullish(),
    updatedAt: z.coerce.date().nullish(),
    reference: z.string().nullish(),
    label: z.string().nullish(),
    purchasePrice: z.coerce.number().nullish(),
    overheads: z.coerce.number().nullish(),
    costPrice: z.coerce.number().nullish(),
    margin: z.coerce.number().nullish(),
    salesUnit: z.coerce.number().nullish(),
    salesPrice: z.coerce.number().nullish(),
    quantity: z.coerce.number().nullish(),
    workCode: z.coerce.number().nullish(),
  })
  .strict();
const relationSchema = z.object({
  organization: z.record(z.unknown()),
});
const fkSchema = z.object({
  organizationId: z.string(),
});

/**
 * `TProductWork` schema excluding foreign keys and relations.
 */
export const TProductWorkScalarSchema = baseSchema.omit({
  deleted: true,
  deletedAt: true,
});

/**
 * `TProductWork` schema including all fields (scalar, foreign key, and relations) and validations.
 */
export const TProductWorkSchema = TProductWorkScalarSchema.merge(
  fkSchema
).merge(relationSchema.partial());

/**
 * Schema used for validating Prisma create input. For internal use only.
 * @private
 */
export const TProductWorkPrismaCreateSchema = baseSchema
  .partial()
  .passthrough();

/**
 * Schema used for validating Prisma update input. For internal use only.
 * @private
 */
export const TProductWorkPrismaUpdateSchema = z
  .object({
    id: z.string(),
    deleted: z.boolean().default(false).nullish(),
    deletedAt: z.coerce.date().nullish(),
    createdAt: z.coerce
      .date()
      .default(() => new Date())
      .nullish(),
    updatedAt: z.coerce.date().nullish(),
    reference: z.string().nullish(),
    label: z.string().nullish(),
    purchasePrice: z.union([
      z.coerce.number().nullish(),
      z.record(z.unknown()),
    ]),
    overheads: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
    costPrice: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
    margin: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
    salesUnit: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
    salesPrice: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
    quantity: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
    workCode: z.union([z.coerce.number().nullish(), z.record(z.unknown())]),
  })
  .partial()
  .passthrough();

/**
 * `TProductWork` schema for create operations excluding foreign keys and relations.
 */
export const TProductWorkCreateScalarSchema = baseSchema.partial({
  id: true,
  deleted: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * `TProductWork` schema for create operations including scalar fields, foreign key fields, and validations.
 */
export const TProductWorkCreateSchema =
  TProductWorkCreateScalarSchema.merge(fkSchema);

/**
 * `TProductWork` schema for update operations excluding foreign keys and relations.
 */
export const TProductWorkUpdateScalarSchema = baseSchema.partial();

/**
 * `TProductWork` schema for update operations including scalar fields, foreign key fields, and validations.
 */
export const TProductWorkUpdateSchema = TProductWorkUpdateScalarSchema.merge(
  fkSchema.partial()
);
