import { db } from "@repo/database/tests";
import { RouterRecord } from "@trpc/server/unstable-core-do-not-import";
import { expect } from "vitest";
import { createContextFromUserId } from "./trpc/handler";
import {
  createCallerFactory,
  Procedure,
  procedure,
  router,
} from "./trpc/server";

/**
 * @param args
 * @returns
 */
export async function createTestContext(
  args: Omit<Parameters<typeof createContextFromUserId>[0], "prisma">
) {
  return createContextFromUserId({
    prisma: db,
    ...args,
  });
}

export const createCallerFactoryForTest = <T extends RouterRecord>(
  routers: (proc: Procedure) => T
) => {
  return createCallerFactory(router(routers(procedure)));
};

/**
 * this function creates:
 * - organization 1
 * - organization 2
 *
 * @returns
 */
export async function createDataContext(props: { domain: string }) {
  const domain = props.domain || "domain.tld";
  const owner = await db.user.create({
    data: {
      firstName: "owner",
      email: `owner@${domain}`,
    },
  });
  const org = await db.organization.create({
    data: {
      name: domain,
      slug: domain,
      website: `https://${domain}`,
      members: { create: { userId: owner.id, role: "owner" } },
    },
  });

  return { org, owner };
}

export async function resetDatabase() {
  await db.tEmail.deleteMany();
  await db.tInvoice.deleteMany();
  await db.tOrder.deleteMany();
  await db.tEquipment.deleteMany();
  await db.tEmail.deleteMany();
  await db.tQuotation.deleteMany();
  await db.tGuy.deleteMany();
  await db.tCompanyContact.deleteMany();
  await db.member.deleteMany();
  await db.invitation.deleteMany();
  await db.user.deleteMany();
  await db.organization.deleteMany();
  await db.tFile.deleteMany({
    where: {
      organizationId: { not: null },
    },
  });
  await db.tCompanySettings.deleteMany();
}

export async function testCRUD<T>({
  caller,
  input,
  inputUpdated,
  model,
}: {
  caller: any;
  model: keyof typeof db;
  input: any;
  inputUpdated:
    | Record<string, any>
    | ((createdItem: any) => Record<string, any>);
}) {
  const { org, owner } = await createDataContext({ domain: "domain.tld" });
  // another org
  const { org: org2 } = await createDataContext({ domain: "domain2.tld" });
  const ctx = await createTestContext({
    userId: owner.id,
    activeOrganizationId: org.id,
  });

  // create
  const item = await caller(ctx)
    [model].create(input)
    .catch((err: any) => {
      console.error(err);
      expect(err).toBeNull();
    });
  let byId = await caller(ctx)
    [model].findOne({ id: item.id })
    .catch((err: any) => {
      console.error(err);
      expect(err).toBeNull();
    });
  expect(byId).toMatchObject(input);

  // check item is created in the right org
  await (db[model] as any).findFirstOrThrow({
    where: { id: item.id, organizationId: org.id },
  });

  const updatedInput =
    typeof inputUpdated === "function" ? inputUpdated(item) : inputUpdated;

  // update
  await caller(ctx)
    [model].update({
      id: item.id,
      ...updatedInput,
    })
    .catch((err: any) => {
      console.error(err);
      expect(err).toBeNull();
    });
  byId = await caller(ctx)
    [model].findOne({ id: item.id })
    .catch((err: any) => expect(err).toBeNull());

  expect(byId).toMatchObject(updatedInput);

  // list
  const list = await caller(ctx)
    [model].findMany()
    .catch((err: any) => expect(err).toBeNull());
  expect(list.items).toHaveLength(1);

  // delete
  await caller(ctx)
    [model].deleteMany({ id: [item.id] })
    .catch((err: any) => expect(err).toBeNull());
  byId = await caller(ctx)
    [model].findOne({ id: item.id })
    .catch((err: any) => expect(err).toBeNull());
  expect(byId).toBeNull();

  return { org, org2, owner, ctx, caller, item };
}
