import { trpcAppRouter } from "@repo/api";
import { db } from "@repo/database";
import { beforeEach, describe, expect, test, vi } from "vitest";

import {
  createDataContext,
  createTestContext,
  resetDatabase,
  testCRUD,
} from "../../../tests";
import { createCallerFactory } from "../../server";

vi.mock("@repo/mail", async () => {
  const actualMailer = await vi.importActual<any>("@repo/mail");

  return {
    ...actualMailer,
    sendEmail: vi.fn().mockResolvedValue({ response: "250 2.0.0 OK" }),
  };
});

describe("TGuy router", () => {
  beforeEach(async () => {
    await resetDatabase();
  });

  test("CRUD", async () => {
    const organization = await db.organization.create({
      data: { name: "org" },
    });
    const tCompanyContact = await db.tCompanyContact.create({
      data: { sirenNumber: "*********", organizationId: organization.id },
    });
    await testCRUD({
      caller: createCallerFactory(trpcAppRouter),
      model: "tGuy",
      input: {
        contactId: tCompanyContact.id,
        firstName: "test",
        lastName: "TEST",
      },
      inputUpdated: {
        firstName: "test2",
        lastName: "TEST2",
      },
    });
  });

  test("as a user, I can invite a new tguy", async () => {
    const { org, owner } = await createDataContext({ domain: "domain.tld" });
    const ctx = await createTestContext({
      userId: owner.id,
      activeOrganizationId: org.id,
    });
    const tCompanyContact = await db.tCompanyContact.create({
      data: { sirenNumber: "*********", organizationId: org.id },
    });
    const caller = createCallerFactory(trpcAppRouter);
    const role = await db.role.findFirstOrThrow();

    const email = "<EMAIL>";

    const tGuy = await caller(ctx).tGuy.create({
      contactId: tCompanyContact.id,
      firstName: "test",
      lastName: "test",
      professionalEmail: email,
      role: role.id,
    });

    const invitation = await db.invitation.findFirstOrThrow({
      where: { email },
    });

    const tEmail = await db.tEmail.findFirstOrThrow({
      where: { to: { has: email } },
    });

    expect(tGuy).toBeTruthy();

    expect(invitation).toMatchObject({
      email,
      role: role.id,
    });

    expect(tEmail).toMatchObject({
      status: "SENT",
      to: expect.arrayContaining([email]),
    });
  });

  test("as a user, I can modify a role", async () => {
    const { org, owner } = await createDataContext({ domain: "domain.tld" });
    const ctx = await createTestContext({
      userId: owner.id,
      activeOrganizationId: org.id,
    });
    const tCompanyContact = await db.tCompanyContact.create({
      data: { sirenNumber: "*********", organizationId: org.id },
    });
    const caller = createCallerFactory(trpcAppRouter);
    const role1 = await db.role.findFirstOrThrow();
    const role2 = await db.role.findFirstOrThrow({
      where: { id: { not: role1.id } },
    });

    const email = "<EMAIL>";

    // simulate user and member since better-auth create them on invitation accept
    const user = await db.user.create({
      data: {
        email,
        firstName: "modify",
        lastName: "test",
      },
    });
    await db.member.create({
      data: {
        user: { connect: { id: user.id } },
        organization: { connect: { id: org.id } },
        Role: { connect: { id: role1.id } },
      },
    });

    const tGuy = await caller(ctx).tGuy.create({
      contactId: tCompanyContact.id,
      firstName: "modify",
      lastName: "test",
      professionalEmail: email,
      role: role1.id,
    });

    await caller(ctx).tGuy.update({
      id: tGuy.id,
      firstName: "modify",
      lastName: "test",
      professionalEmail: email,
      role: role2.id,
    });

    const authMember = await db.member.findFirstOrThrow({
      where: {
        userId: user.id,
        organizationId: org.id,
      },
    });

    expect(authMember.role).toBe(role2.id);
  });

  test("as a user, I can set a role to null", async () => {
    const { org, owner } = await createDataContext({ domain: "domain.tld" });
    const ctx = await createTestContext({
      userId: owner.id,
      activeOrganizationId: org.id,
    });
    const tCompanyContact = await db.tCompanyContact.create({
      data: { sirenNumber: "*********", organizationId: org.id },
    });
    const caller = createCallerFactory(trpcAppRouter);
    const role = await db.role.findFirstOrThrow();

    const email = "<EMAIL>";

    const tGuy = await caller(ctx).tGuy.create({
      contactId: tCompanyContact.id,
      firstName: "nullrole",
      lastName: "test",
      professionalEmail: email,
      role: role.id,
    });

    const invitationBefore = await db.invitation.findFirst({
      where: {
        email,
        organizationId: org.id,
      },
    });
    expect(invitationBefore).toBeTruthy();

    await caller(ctx).tGuy.update({
      id: tGuy.id,
      role: null,
    });

    const invitationAfter = await db.invitation.findFirst({
      where: {
        email,
        organizationId: org.id,
      },
    });

    expect(invitationAfter).toBeNull();
  });

  test("as a user, I cannot modify my own role", async () => {
    const { org, owner } = await createDataContext({ domain: "domain.tld" });
    const ctx = await createTestContext({
      userId: owner.id,
      activeOrganizationId: org.id,
    });
    const tCompanyContact = await db.tCompanyContact.create({
      data: { sirenNumber: "*********", organizationId: org.id },
    });
    const caller = createCallerFactory(trpcAppRouter);
    const role = await db.role.findFirstOrThrow();

    const tGuy = await caller(ctx).tGuy.create({
      contactId: tCompanyContact.id,
      firstName: owner.firstName,
      lastName: owner.lastName,
      professionalEmail: owner.email,
      role: role.id,
    });

    const newRole = await db.role.findFirstOrThrow({
      where: { id: { not: role.id } },
    });

    await expect(
      caller(ctx).tGuy.update({
        id: tGuy.id,
        role: newRole.id,
      })
    ).rejects.toThrow();
  });
});
