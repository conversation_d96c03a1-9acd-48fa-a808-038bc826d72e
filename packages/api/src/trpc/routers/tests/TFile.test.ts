import { trpcAppRouter } from "@repo/api";
import { db } from "@repo/database/tests";
import { beforeEach, describe, expect, test } from "vitest";
import {
  createDataContext,
  createTestContext,
  resetDatabase,
  testCRUD,
} from "../../../tests";
import { createCallerFactory } from "../../server";
import { PUBLIC_FILES } from "../custom/TFile.router";

describe("TFile router", () => {
  beforeEach(async () => {
    await resetDatabase();
  });

  test("CRUD", async () => {
    await testCRUD({
      caller: createCallerFactory(trpcAppRouter),
      model: "tFile",
      input: {
        filename: "test",
        url: "http://test.com",
        typeId: "identity_card",
      },
      inputUpdated: {
        filename: "test2",
      },
    });
  });

  test("Should show only public files if no organization is selected", async () => {
    const { org, owner } = await createDataContext({ domain: "domain.tld" });

    const ctx = await createTestContext({
      userId: owner.id,
      activeOrganizationId: org.id,
    });

    // get length of seed files
    const seedFilesLength = await db.tFile.count({
      where: {
        organizationId: null,
      },
    });

    const privateFile = await db.tFile.create({
      data: {
        filename: "test",
        url: "http://test.com",
        typeId: "identity_card",
        organizationId: org.id,
      },
    });

    const caller = createCallerFactory(trpcAppRouter);

    const { items } = await caller(ctx).tFile.findMany();

    expect(items).toHaveLength(1);
    expect(items[0].id).toBe(privateFile.id);

    const { items: publicFiles } = await caller(ctx).tFile.findMany({
      where: {
        organizationId: null,
      },
    });

    console.log(publicFiles);

    expect(publicFiles).toHaveLength(seedFilesLength);
    expect(publicFiles).not.toContainEqual(privateFile);
    expect(PUBLIC_FILES).toContainEqual(publicFiles[0].typeId);
  });

  // test("Should show public and organization files if organization is selected", async () => {
  //   const { org, owner } = await createDataContext({ domain: "domain.tld" });

  //   const ctx = await createTestContext({
  //     userId: owner.id,
  //     activeOrganizationId: org.id,
  //   });
  // });

  // test("Should show only organization files if organization is selected with filter of no public files", async () => {
  //   const { org, owner } = await createDataContext({ domain: "domain.tld" });

  //   const ctx = await createTestContext({
  //     userId: owner.id,
  //     activeOrganizationId: org.id,
  //   });
  // });
});
