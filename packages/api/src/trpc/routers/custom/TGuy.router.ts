import { generate<PERSON>odel<PERSON>ode, Prisma, type TGuy } from "@repo/database";
import { getBaseUrl } from "@repo/utils";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { ignoreInternalFields } from "../../../utils";
import { TGuyCreateSchema, TGuyUpdateSchema } from "../../../validation";
import { TGuyWhereInputObjectSchema } from "../../../zod/objects/TGuyWhereInput.schema";
import { Procedure } from "../../server";
import { createTGuyRouter as generatedRouter } from "../generated/TGuy.router";
import { checkMutate, checkRead, db, sendAndSaveEmail } from "../helper";

async function inviteMember(
  ctx: any,
  email: string,
  role: string,
  existingUser: boolean
) {
  const org = await db(ctx).organization.findUnique({
    where: { id: ctx.currentOrganizationId as string },
  });
  if (!org?.id) return null;

  const invitation = await db(ctx).invitation.create({
    data: {
      email,
      organization: { connect: { id: org.id } },
      status: "pending",
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // +7 days
      Role: { connect: { id: role } },
      user: { connect: { id: ctx.currentUserId } },
    },
  });

  if (!invitation) throw new Error("Failed to create invitation");

  const url = new URL(
    existingUser ? "/auth/login" : "/auth/signup",
    getBaseUrl()
  );

  url.searchParams.set("invitationId", invitation.id);
  url.searchParams.set("email", email);

  await sendAndSaveEmail(ctx, {
    to: [email],
    templateId: "organizationInvitation",
    context: {
      organizationName: org?.name ?? "",
      url: url.toString(),
    },
  });
}

export function createTGuyRouter(procedure: Procedure) {
  return generatedRouter(procedure, {
    listFilterSchema: (TGuyWhereInputObjectSchema as any as z.ZodObject<any>) // TODO improve typing here
      .pick({
        AND: true,
        firstName: true,
        lastName: true,
      }),
    applySearch: (q?: string) =>
      ({
        OR: [
          { firstName: { contains: q, mode: "insensitive" } },
          { lastName: { contains: q, mode: "insensitive" } },
        ],
      }) as Prisma.TGuyWhereInput,
    extraRoutes: {
      create: procedure
        .input(
          ignoreInternalFields(
            TGuyCreateSchema.merge(z.object({ role: z.string().nullish() }))
          )
        )
        .mutation(async ({ ctx, input }) => {
          if (input.role && !input.professionalEmail)
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "Cannot assign a role to a collaborator without an email.",
            });
          const code = await generateModelCode(ctx, "tGuy");
          const data = { ...input, code } as Prisma.TGuyUncheckedCreateInput;

          if (input.role && input.professionalEmail) {
            const foundUser = await db(ctx).user.findFirst({
              where: { email: input.professionalEmail },
            });
            if (foundUser) data.linkedUserId = foundUser.id;
            await inviteMember(
              ctx,
              input.professionalEmail,
              input.role,
              Boolean(foundUser)
            );
          }

          delete (data as any)?.role;
          return db(ctx).tGuy.create({ data });
        }),
      findOne: procedure
        .input(z.object({ id: z.string() }))
        .query(async ({ ctx, input }) => {
          const guy: (TGuy & { role?: string }) | null = await checkRead(
            db(ctx).tGuy.findUnique({ where: { id: input.id } })
          );
          if (!guy) return null;
          // No role attributed yet
          if (!guy.professionalEmail) return guy;

          // Already member
          const member = await db(ctx).member.findFirst({
            where: { user: { email: guy.professionalEmail } },
          });
          if (member) return { ...guy, role: member.role };

          // Invitation pending
          const invitation = await db(ctx).invitation.findFirst({
            where: { email: guy.professionalEmail },
          });
          if (invitation) return { ...guy, role: invitation.role };

          return guy;
        }),
      update: procedure
        .input(
          ignoreInternalFields(
            TGuyUpdateSchema.merge(z.object({ role: z.string().nullish() }))
          )
        )
        .mutation(async ({ ctx, input }) => {
          const guy = await db(ctx).tGuy.findFirstOrThrow({
            where: { id: input.id },
          });

          // Prevent user from updating their own role
          if (input.role && guy.linkedUserId === ctx.currentUserId)
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "You cannot update your own role.",
              cause: "Self-role update is not allowed.",
            });

          // Cannot assign a role if professionalEmail is missing
          if (input.role && !input.professionalEmail)
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "Cannot assign a role to a collaborator without an email.",
            });

          const foundUser = await db(ctx).user.findFirst({
            where: { email: input.professionalEmail },
          });

          // If a role is provided and the guy is not yet linked to a user, link or invite
          if (input.role && !guy.linkedUserId) {
            if (foundUser) input.linkedUserId = foundUser.id;
            await inviteMember(
              ctx,
              input.professionalEmail,
              input.role,
              Boolean(foundUser)
            );
          }

          // If a role is provided and the guy is already linked, update the member's role in the organization
          if (input.role && guy.linkedUserId && ctx.currentOrganizationId) {
            await db(ctx).member.update({
              where: {
                userId_organizationId: {
                  userId: guy.linkedUserId,
                  organizationId: ctx.currentOrganizationId,
                },
              },
              data: { role: input.role },
            });
          }

          // If no role is provided but the guy is linked and has an email, remove invitations and membership
          if (
            !input.role &&
            ctx.currentOrganizationId &&
            guy.professionalEmail
          ) {
            await db(ctx).invitation.deleteMany({
              where: { email: guy.professionalEmail },
            });
            if (guy.linkedUserId)
              await db(ctx).member.deleteMany({
                where: {
                  userId: guy.linkedUserId,
                  organizationId: ctx.currentOrganizationId,
                },
              });
            input.linkedUserId = null;
          }

          delete input.role;
          return db(ctx).tGuy.update({
            where: { id: input.id },
            data: input,
          });
        }),
      stats: procedure
        .input(z.object({ where: TGuyWhereInputObjectSchema }).optional())
        .query(async ({ ctx, input }) => {
          const total = await db(ctx).tGuy.count(
            input?.where ? { where: input.where } : undefined
          );
          const active = await db(ctx).tGuy.count({
            where: input?.where
              ? { ...input.where, active: true }
              : { active: true },
          });
          return {
            total,
            active,
            inactive: total - active,
          };
        }),
      deleteMany: procedure
        .input(
          z
            .object({
              id: z.string().array().optional(),
              where: (
                TGuyWhereInputObjectSchema as any as z.ZodObject<any>
              ).optional(),
            })
            .refine((data) => data.id || data.where, {
              message: "Either 'id' or 'where' must be provided",
            })
        )
        .mutation(async ({ ctx, input }) => {
          const where = input?.where ?? { id: { in: input.id } };
          return checkMutate(
            db(ctx).tGuy.deleteMany({
              where,
            })
          );
        }),
    },
  });
}
