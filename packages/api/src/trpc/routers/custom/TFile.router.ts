import { env } from "@repo/config/server";
import { $Enums, LDOCUMENT_KEY, Prisma } from "@repo/database";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import {
  signAwsS3GetRequest,
  signAwsS3UploadRequest,
} from "../../../providers/s3";
import { findManySchema, ignoreInternalFields } from "../../../utils";
import { TFileCreateSchema } from "../../../zod/models/TFile.schema";
import { TFileWhereInputObjectSchema } from "../../../zod/objects";
import { type Procedure } from "../../server";
import { createTFileRouter as generatedRouter } from "../generated/TFile.router";
import { checkRead, db } from "../helper";

const getExtension = (name: string) => name.split(".").pop();

export const PUBLIC_FILES = [$Enums.LDocumentKey.avatar];

export function createTFileRouter(procedure: Procedure) {
  return generatedRouter(procedure, {
    applySearch: (q: string) =>
      ({
        OR: [{ name: { contains: q } }],
      } as Prisma.TFileWhereInput),
    extraRoutes: {
      create: procedure
        .input(ignoreInternalFields(TFileCreateSchema))
        .mutation(async ({ ctx, input }) => {
          // note we replace the private url https://<bucket>.<accountId>r2.cloudflarestorage.com
          // by the public url
          // TODO also support private url for cloudflare
          const url = input.url.replace(
            env.AWS_S3_ENDPOINT_URL.replace(
              "https://",
              "https://" + env.AWS_S3_BUCKET + "."
            ),
            env.AWS_S3_ENDPOINT_PUBLIC_URL
          );
          return db(ctx).tFile.create({
            data: { ...input, url } as Prisma.TFileCreateInput,
          });
        }),
      signUploadUrl: procedure
        .input(
          z.object({
            filename: z.string(),
            documentTypeKey: z.enum(LDOCUMENT_KEY),
          })
        )
        .mutation(async ({ input, ctx }) => {
          if (!ctx.currentOrganizationId) {
            throw new TRPCError({
              code: "UNAUTHORIZED",
              message: "No organization selected",
            });
          }

          const documentType = await ctx.prisma.lDocumentType.findFirstOrThrow({
            where: { id: input.documentTypeKey },
          });

          const tFile = await db(ctx).tFile.create({
            data: {
              typeId: documentType.id,
            } as unknown as Prisma.TFileCreateInput,
          });

          const folder = `company/${ctx.currentOrganizationId}/${documentType.key}`;
          const filename = `${tFile.id}.${getExtension(input.filename)}`;

          const { url, presignedUrl } = await signAwsS3UploadRequest({
            fileName: `${folder}/${filename}`,
            bucket: env.AWS_S3_BUCKET,
          });

          const _url = url.replace(
            env.AWS_S3_ENDPOINT_URL.replace(
              "https://",
              "https://" + env.AWS_S3_BUCKET + "."
            ),
            env.AWS_S3_ENDPOINT_PUBLIC_URL
          );

          // url: The public URL used to access the file after upload (stored in DB)
          // presignedUrl: Temporary URL with write permissions to upload the file
          // Both URLs point to the same resource but serve different purposes:
          // - presignedUrl expires after 60s and has PUT permissions
          // - url is permanent and only has GET permissions through public endpoint
          return {
            url: _url,
            typeId: documentType.id,
            presignedUrl,
            tFileId: tFile.id,
          };
        }),
      signDownloadUrl: procedure
        .input(z.object({ url: z.string() }))
        .mutation(async ({ input }) => {
          const { signedUrl } = await signAwsS3GetRequest({
            url: input.url,
          });
          return { signedUrl };
        }),
      findMany: procedure
        .input(
          findManySchema(
            (TFileWhereInputObjectSchema as any as z.ZodObject<any>).pick({
              AND: true,
              typeId: true,
              organizationId: true,
            })
          )
        )
        .query(async ({ ctx, input }) => {
          let where: Prisma.TFileWhereInput = input.where || {};
          if (input.q) {
            where = {
              AND: [
                { ...where },
                {
                  OR: [{ name: { contains: input.q } }],
                },
              ],
            };
          }

          where = {
            AND: [
              { ...where },
              {
                OR: [
                  { organizationId: null },
                  { organizationId: ctx.currentOrganizationId },
                ],
              },
            ],
          };

          const page = input.page || 0;
          const take = input.take || 10;

          const context = ctx.prismaOriginal.tFile;

          const [items, total] = await checkRead(
            Promise.all([
              context.findMany({
                where,
                include: {
                  type: { select: { id: true, label: true } },
                },
                skip: (page - 1) * take,
                take,
              }),
              context.count({
                where,
              }),
            ])
          );

          const totalPages = Math.ceil(total / take);

          return {
            items,
            pagination: {
              total,
              page,
              take,
              totalPages,
            },
          };
        }),
    },
  });
}
