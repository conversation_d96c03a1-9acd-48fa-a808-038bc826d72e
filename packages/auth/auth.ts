import { config } from "@repo/config";
import { env } from "@repo/config/server";
import { db } from "@repo/database";
import type { Locale } from "@repo/i18n";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import {
  createAuthMiddleware,
  magicLink,
  openAPI,
  organization,
  // username,
} from "better-auth/plugins";
import { passkey } from "better-auth/plugins/passkey";
import { parse as parseCookies } from "cookie";
import { getPappersInfo } from "../api/src/services/pappers";
// import { invitationOnlyPlugin } from "./plugins/invitation-only";
import { TCompanyContactCreateSchema } from "../api/src/zod/models";
import { InfoPapperResult } from "../types";
import { getUserByEmail } from "./lib/user";

const getLocaleFromRequest = (request?: Request) => {
  const cookies = parseCookies(request?.headers.get("cookie") ?? "");
  return (
    (cookies[config.i18n.localeCookieName] as Locale) ??
    config.i18n.defaultLocale
  );
};

const appUrl = getBaseUrl();

export const auth = betterAuth({
  baseURL: appUrl,
  trustedOrigins: [appUrl],
  database: prismaAdapter(db, {
    provider: "postgresql",
  }),
  advanced: {
    cookiePrefix: "vispacem",
  },
  session: {
    expiresIn: config.auth.sessionCookieMaxAge,
    cookieCache: {
      enabled: true,
      maxAge: 60,
    },
    freshAge: 0,
  },
  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google"],
    },
  },
  hooks: {
    after: createAuthMiddleware(async (ctx) => {
      if (ctx.path.startsWith("/organization/accept-invitation")) {
        const { invitationId } = ctx.body;

        if (!invitationId) {
          return;
        }

        const invitation = await db.invitation.findUnique({
          where: { id: invitationId },
        });

        if (!invitation) {
          return;
        }

        const user = await db.user.findFirst({
          where: { email: invitation.email },
        });

        await db.tGuy.updateMany({
          where: { professionalEmail: invitation.email },
          data: {
            linkedUserId: user?.id,
          },
        });
      } else if (ctx.path.startsWith("/organization/remove-member")) {
        const { organizationId } = ctx.body;

        if (!organizationId) {
          return;
        }
      }
    }),
  },
  user: {
    additionalFields: {
      // onboardingComplete: {
      //   type: "boolean",
      //   required: false,
      // },
      locale: {
        type: "string",
        required: false,
      },
    },
    deleteUser: {
      enabled: true,
    },
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async (
        { user: { email, name }, url },
        request
      ) => {
        const locale = getLocaleFromRequest(request);
        await sendEmail({
          to: email,
          templateId: "emailVerification",
          context: {
            url,
            name,
          },
          locale,
        });
      },
    },
  },
  emailAndPassword: {
    enabled: true,
    // If signup is disabled, the only way to sign up is via an invitation. So in this case we can auto sign in the user, as the email is already verified by the invitation.
    // If signup is enabled, we can't auto sign in the user, as the email is not verified yet.
    autoSignIn: !config.auth.enableSignup,
    requireEmailVerification: config.auth.enableSignup,
    sendResetPassword: async ({ user, url }, request) => {
      const locale = getLocaleFromRequest(request);
      await sendEmail({
        to: user.email,
        templateId: "forgotPassword",
        context: {
          url,
          name: user.name,
        },
        locale,
      });
    },
  },
  emailVerification: {
    sendOnSignUp: config.auth.enableSignup,
    sendVerificationEmail: async ({ user: { email, name }, url }, request) => {
      const locale = getLocaleFromRequest(request);
      await sendEmail({
        to: email,
        templateId: "emailVerification",
        context: {
          url,
          name,
        },
        locale,
      });
    },
  },
  socialProviders: {
    google: {
      clientId: env.GOOGLE_CLIENT_ID as string,
      clientSecret: env.GOOGLE_CLIENT_SECRET as string,
      scope: ["email", "profile"],
    },
  },
  plugins: [
    //  wraps the email and password authenticator and adds username support
    // fields needed: https://www.better-auth.com/docs/plugins/username#schema
    // username(),
    // admin: create/ban/unban/impersonate users, manage user roles,...
    // fields needed: https://www.better-auth.com/docs/plugins/admin#schema
    // admin(),
    passkey(),
    magicLink({
      disableSignUp: true,
      sendMagicLink: async ({ email, url }, request) => {
        const locale = getLocaleFromRequest(request);
        await sendEmail({
          to: email,
          templateId: "magicLink",
          context: {
            url,
          },
          locale,
        });
      },
    }),
    organization({
      schema: {
        organization: {
          fields: {
            sirenNumber: "sirenNumber",
          } as any,
        },
      },
      organizationCreation: {
        // disabled: false, // Set to true to disable organization creation
        beforeCreate: async ({ organization, user: _user }) => {
          const pappersData = await getPappersInfo({
            q: organization.metadata.sirenNumber,
          });
          const data = pappersData.data as InfoPapperResult;
          const meta = organization.metadata || {};
          return {
            data: {
              ...organization,
              name:
                organization.name ??
                data.denomination ??
                data.nom_entreprise ??
                "",
              metadata: {
                sirenNumber: data.siren,
                siretNumber: data.siege.siret,
                legalForm: meta.legalForm ?? data.forme_juridique,
                address: meta.address ?? data.siege.adresse_ligne_1,
                postalCode: meta.postalCode ?? data.siege.code_postal,
                city: meta.city ?? data.siege.ville,
                capital: meta.capital ?? data.capital,
                codeNaf: meta.codeNaf ?? data.code_naf,
                phone: meta.phone,
                website: meta.website,
                email: meta.email,
                registrationDateRne: data.date_immatriculation_rne,
                registrationDateRcs: data.date_immatriculation_rcs,
                creationDate: data.date_creation,
                collectiveAgreements: data.conventions_collectives?.map(
                  (c) => c.idcc
                ),
              },
            },
          };
        },
        afterCreate: async ({ organization, user /* member, user */ }) => {
          // note: this is a trick to save extra fields to the organization,
          await db.$transaction(async (tx) => {
            // remove non-mapped data from organization
            const data = await TCompanyContactCreateSchema.parseAsync({
              organizationId: organization.id,
              ...organization,
              ...organization.metadata,
            });

            const tContact = await tx.tContact.create({
              data: {
                ...data,
                code: "0",
                familyId: 1, // entreprise - see LContactFamily.seed
              },
            });

            await tx.organization.update({
              where: { id: organization.id },
              data: {
                metadata: null as any,
              },
            });

            const tCompanyContact = await tx.tCompanyContact.create({
              data: {
                organizationId: organization.id,
                contactId: tContact.id,
              },
            });

            const foundUser = await tx.user.findUnique({
              where: { id: user.id },
            });

            const tGuy = await tx.tGuy.findFirst({
              where: { linkedUserId: user.id },
            });

            if (foundUser && !tGuy) {
              await tx.tGuy.create({
                data: {
                  ...parseFullName({
                    name: foundUser.name,
                    firstName: foundUser.firstName,
                    lastName: foundUser.lastName,
                  }),
                  professionalEmail: foundUser.email,
                  contactId: tCompanyContact.id,
                  organizationId: organization.id,
                  active: foundUser.active,
                  position: foundUser.position,
                  postalCode: foundUser.postalCode,
                  address: foundUser.address,
                  city: foundUser.city,
                  mondays: foundUser.mondays,
                  tuesdays: foundUser.tuesdays,
                  wednesdays: foundUser.wednesdays,
                  thursdays: foundUser.thursdays,
                  fridays: foundUser.fridays,
                  saturdays: foundUser.saturdays,
                  sundays: foundUser.sundays,
                  linkedUserId: foundUser.id,
                },
              });
            }

            await tx.tCompanySettings.create({
              data: { organizationId: organization.id },
            });
          });
        },
      },
      sendInvitationEmail: async ({ email, id, organization }, request) => {
        const locale = getLocaleFromRequest(request);
        const existingUser = await getUserByEmail(email);

        const url = new URL(
          existingUser ? "/auth/login" : "/auth/signup",
          getBaseUrl()
        );

        url.searchParams.set("invitationId", id);
        url.searchParams.set("email", email);

        await sendEmail({
          to: email,
          templateId: "organizationInvitation",
          locale,
          context: {
            organizationName: organization.name,
            url: url.toString(),
          },
        });
      },
    }),
    openAPI(),
    // invitationOnlyPlugin(),
  ],
  onAPIError: {
    onError(error, _ctx) {
      console.error("onAPIError", error?.toString());
      // logger.error("onAPIError", error?.toString(), { ctx });
    },
  },
});

type FullNameInput = {
  name?: string | null;
  firstName?: string | null;
  lastName?: string | null;
};

function parseFullName({ name, firstName, lastName }: FullNameInput) {
  if (firstName?.trim() || lastName?.trim()) {
    return {
      firstName: firstName?.trim() || "",
      lastName: lastName?.trim() || "",
    };
  }
  if (!name || !name.trim()) {
    return { firstName: "", lastName: "" };
  }
  const parts = name.trim().split(/\s+/);
  if (parts.length === 1) {
    return { firstName: parts[0], lastName: "" };
  }
  return {
    firstName: parts[0],
    lastName: parts.slice(1).join(" "),
  };
}

export * from "./lib/organization";

export type Session = typeof auth.$Infer.Session;

export type ActiveOrganization = typeof auth.$Infer.ActiveOrganization;

export type Organization = typeof auth.$Infer.Organization;

export type OrganizationMemberRole = typeof auth.$Infer.Member.role;

export type OrganizationInvitationStatus = typeof auth.$Infer.Invitation.status;

export type OrganizationMetadata = Record<string, unknown> | undefined;
