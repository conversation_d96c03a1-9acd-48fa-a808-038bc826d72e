import { LabelledAddressField } from "@shared/components/common/textfield/LabelledAddressField";
import { LabelledCentimesVFTF } from "@shared/components/common/textfield/LabelledCentimesVFTF";
import { LabelledEmailVFTF } from "@shared/components/common/textfield/LabelledEmailVFTF";
import { LabelledPhoneVFTF } from "@shared/components/common/textfield/LabelledPhoneVFTF";
import { LabelledSelectMultipleInput } from "@shared/components/common/textfield/LabelledSelectMultipleInput";
import { LabelledUrlField } from "@shared/components/common/textfield/LabelledUrlField";
import { LabelledVTextField } from "@shared/components/common/textfield/LabelledVTextField";
import { useLCodeNaf } from "@shared/hooks/models/generated/useLCodeNaf";
import { useLLegalForm } from "@shared/hooks/models/generated/useLLegalForm";
import { Info } from "lucide-react";
import { useFormContext } from "react-hook-form";

interface OrganizationFormProps {
  formData: SignupFormData;
  onFormChange: (data: SignupFormData) => void;
}

interface SignupFormData {
  siren: string;
  name: string;
  capital: string;
  vatNumber: string;
  naf: string;
  phone: string;
  address: string;
  postalCode: string;
  city: string;
  email: string;
  legalForm: string;
}

export function OrganizationForm({ formData }: OrganizationFormProps) {
  const hasCompanyData = formData.name && formData.siren;

  const { control, register, setValue } = useFormContext();
  const { autocomplete: codeNafOptions } = useLCodeNaf({ autocomplete: true });
  const { autocomplete: LegalFormOptions } = useLLegalForm({
    autocomplete: true,
  });

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className={`${hasCompanyData ? "relative" : ""}`}>
        {hasCompanyData && (
          <div className="absolute -top-2 right-0 text-xs text-orange-600 flex items-center gap-1">
            <Info className="w-4 h-4" />
            <span>Informations récupérées automatiquement</span>
          </div>
        )}
        <div className="grid md:grid-cols-2 items-start gap-4">
          <LabelledVTextField
            label="SIREN"
            {...register("sirenNumber")}
            name="siren"
          />
          <LabelledPhoneVFTF label="Téléphone" name="phone" control={control} />
          <LabelledVTextField
            label={"Dénomination"}
            {...register("name")}
            className="w-full"
            required
          />
          <LabelledEmailVFTF
            label="Email"
            {...register("email")}
            className="w-full"
          />
          <LabelledSelectMultipleInput
            isMulti={false}
            label="Forme juridique"
            name="legalFormId"
            control={control}
            options={LegalFormOptions.options ?? []}
            placeholder="Sélectionner..."
            className="w-full"
          />
          <LabelledUrlField
            label={"Site web"}
            {...register("website")}
            className="w-full"
          />
          <LabelledCentimesVFTF label={"Capital"} {...register("capital")} />
          <LabelledSelectMultipleInput
            isMulti={false}
            label={"Code NAF"}
            name="codeNaf"
            control={control}
            options={codeNafOptions.options ?? []}
            placeholder="Sélectionner un code NAF"
            className="w-full"
          />
        </div>
      </div>
      {/* Adresse */}
      <div>
        <div className="space-y-4">
          <LabelledAddressField
            register={register}
            setValue={setValue}
            control={control}
          />
        </div>
      </div>
    </div>
  );
}

export default OrganizationForm;
