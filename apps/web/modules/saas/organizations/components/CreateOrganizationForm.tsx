"use client";

import { TCompanyContactCreateSchema } from "@repo/api/validation";
import { Organization } from "@repo/auth";
import { removeWhiteSpace } from "@repo/utils";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
  organizationListQueryKey,
  useCreateOrganizationMutation,
} from "@saas/organizations/lib/api";
import { customResolver } from "@shared/components/common/form/util";
import { useCompanyInfo } from "@shared/hooks/services/useCompanyInfo";
import { useQueryClient } from "@tanstack/react-query";
import {
  AutoComplete,
  Option as BaseOption,
} from "@ui/components/autocomplete";
import { Button } from "@ui/components/button";
import { Form } from "@ui/components/form";
import { useToast } from "@ui/hooks/use-toast";
import { useDebounce } from "@uidotdev/usehooks";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Dialog, DialogTrigger } from "../../../ui/components/dialog";
import { OrganizationRegistrationModal } from "./CreateOrganizationRegistrationModal";

// Types
type Option = BaseOption & {
  additional?: {
    postalCode?: string;
    city?: string;
    legalFormId?: number;
    capital?: string;
    email?: string;
    phone?: string;
    codeNaf?: string;
    address?: string;
    [key: string]: any;
  };
};

type FormValues = z.infer<typeof TCompanyContactCreateSchema>;

// Main Component
export function CreateOrganizationForm() {
  const t = useTranslations();
  const { toast } = useToast();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { setActiveOrganization } = useActiveOrganization();
  const createOrganizationMutation = useCreateOrganizationMutation();
  const { searchBySiren, searchByName } = useCompanyInfo();

  const form = useForm<FormValues>({
    resolver: customResolver(TCompanyContactCreateSchema),
  });

  // State
  const [isOpen, setIsOpen] = useState(false);
  const [errors, setErrors] = useState("");

  const [sirenSearchValue, setSirenSearchValue] = useState("");
  const debouncedSirenSearchValue = useDebounce(sirenSearchValue, 500);
  const [sirenValue, setSirenValue] = useState<Option | undefined>();
  const [sirenLoading, setSirenLoading] = useState(false);
  const [sirenCompletions, setSirenCompletions] = useState<Option[]>([]);

  const [nameSearchValue, setNameSearchValue] = useState("");
  const debouncedNameSearchValue = useDebounce(nameSearchValue, 500);
  const [nameValue, setNameValue] = useState<Option | undefined>();
  const [nameLoading, setNameLoading] = useState(false);
  const [nameCompletions, setNameCompletions] = useState<Option[]>([]);

  const [loading, setLoading] = useState<boolean>(false);

  const ref = useRef<HTMLButtonElement>(null);

  const searchCompany = async (
    searchFn: (value: string) => Promise<{ data: any | null }> | { data: null },
    value: string,
    setLoading: (value: boolean) => void,
    onCompanyFound: (data: any) => void,
    errorMessage: string
  ): Promise<void> => {
    setErrors("");
    if (!value.trim().length) return;

    setLoading(true);
    try {
      const { data: companyData } = await searchFn(value);

      if (companyData) onCompanyFound(companyData);
      else setErrors(errorMessage);
    } catch (error) {
      console.error("Error searching company", error);
      setErrors(errorMessage);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 100);
    }
  };

  const searchWithSiren = useCallback(
    () =>
      searchCompany(
        (siren) => {
          const cleanSiren = removeWhiteSpace(siren);
          if (!/^\d{9}$/.test(cleanSiren)) return { data: null };
          return searchBySiren({ siren: Number.parseInt(cleanSiren) });
        },
        sirenSearchValue,
        setSirenLoading,
        (company: any) => {
          if (company)
            setSirenCompletions([
              {
                label: company.siren,
                value: company.siren,
                additional: {
                  value: company.denomination ?? company.nom_entreprise ?? "",
                  label: `${company.denomination ?? company.nom_entreprise ?? ""} ${company.siege.code_postal ? `(${company.siege.code_postal})` : ""}`,
                  capital: company.capital ?? "",
                  phone: company.phone ?? "",
                  email: company.email ?? "",
                  address: company.siege.adresse_ligne_1 ?? "",
                  postalCode: company.siege.code_postal ?? "",
                  city: company.siege.ville ?? "",
                  legalFormId: company.categorie_juridique ?? "",
                  codeNaf: company.code_naf ?? "",
                },
                render: (
                  <p className="text-xs">
                    <b>
                      {company.denomination ?? company.nom_entreprise ?? ""}
                    </b>{" "}
                    <span className="mt-2">
                      ({company.siren}) <br />
                      {company.siege.code_postal ?? ""}{" "}
                      {company.siege.ville ?? ""}
                    </span>
                  </p>
                ),
              },
            ]);
        },
        "Erreur lors de la recherche. Veuillez vérifier le SIREN saisi."
      ),
    [sirenSearchValue]
  );

  const searchWithName = useCallback(
    () =>
      searchCompany(
        (name) => searchByName({ name: name.trim() }),
        nameSearchValue,
        setNameLoading,
        (data: any) => {
          if (data.length)
            setNameCompletions(
              data.map((company) => ({
                label: company.denomination,
                value: company.siren,
                additional: {
                  value: company.denomination ?? company.nom_entreprise ?? "",
                  label: company.siren,
                  capital: company.capital ?? "",
                  phone: company.phone ?? "",
                  address: company.siege.adresse_ligne_1 ?? "",
                  postalCode: company.siege.code_postal ?? "",
                  email: company.email ?? "",
                  city: company.siege.ville ?? "",
                  legalFormId: company.categorie_juridique ?? "",
                  codeNaf: company.code_naf ?? "",
                },
                render: (
                  <p className="text-xs">
                    <b>
                      {company.denomination ?? company.nom_entreprise ?? ""}
                    </b>{" "}
                    <span className="mt-2">
                      ({company.siren}) <br />
                      {company.siege.code_postal ?? ""}{" "}
                      {company.siege.ville ?? ""}
                    </span>
                  </p>
                ),
              }))
            );
        },
        "Aucun résultat trouvé pour ce nom. Veuillez vérifier l'orthographe ou essayer un autre nom."
      ),
    [nameSearchValue]
  );

  useEffect(() => {
    searchWithSiren();
  }, [debouncedSirenSearchValue]);

  useEffect(() => {
    searchWithName();
  }, [debouncedNameSearchValue]);

  const handleOpenChange = (open: boolean) => {
    if (open && !sirenValue && !nameValue) return;
    setIsOpen(open);
  };

  const onSubmit = form.handleSubmit(async (values) => {
    setLoading(true);
    try {
      const newOrganization = await createOrganizationMutation.mutateAsync({
        ...values,
        name: values.name ?? "",
        metadata: {
          sirenNumber: values.sirenNumber,
          legalFormId: values.legalFormId,
          address: values.address,
          postalCode: values.postalCode,
          city: values.city,
          capital: values.capital,
          codeNaf: values.codeNaf,
          phone: values.phone,
          website: values.website,
          email: values.email,
          collectiveAgreements: values.collectiveAgreements,
        } as Partial<Organization>,
      });
      if (!newOrganization) throw new Error("Failed to create organization");

      await setActiveOrganization(newOrganization.id);
      await queryClient.invalidateQueries({
        queryKey: organizationListQueryKey,
      });
      router.replace(`/fr/${newOrganization.slug}`);

      toast({
        variant: "success",
        title: t("organizations.createForm.notifications.success"),
      });
      setIsOpen(true);
    } catch (e: any) {
      console.error("Failed to create organization", e);
      toast({
        title: t("organizations.createForm.notifications.error"),
        variant: "error",
      });
    } finally {
      setLoading(false);
    }
  });

  // Render
  return (
    <div className="mx-auto w-full max-w-md">
      <h1 className="font-extrabold text-2xl md:text-3xl">
        {t("organizations.createForm.title")}
      </h1>
      <p className="mt-2 mb-6 text-foreground/60">
        {t("organizations.createForm.subtitle")}
      </p>
      <Form {...form}>
        <form onSubmit={onSubmit}>
          <AutoComplete
            value={nameValue}
            isLoading={nameLoading || sirenLoading}
            placeholder="Entrez le SIREN ou le nom de l'entreprise"
            emptyMessage="Pas de resultat"
            options={[...sirenCompletions, ...nameCompletions]}
            onSearchChange={(s) => {
              setErrors("");
              const cleanInput = removeWhiteSpace(s);
              if (/^\d{9,14}$/.test(cleanInput)) {
                const sirenOnly = cleanInput.slice(0, 9);
                setSirenSearchValue(sirenOnly);
                setNameSearchValue("");
                if (cleanInput.length > 9) {
                  form.setValue("sirenNumber", sirenOnly);
                }
              } else {
                setNameSearchValue(s);
                setSirenSearchValue("");
              }
            }}
            onValueChange={(t) => {
              setSirenValue(t);
              setNameValue(t);
              form.setValue(
                "sirenNumber",
                removeWhiteSpace(t.value.toString())
              );
              if (t.additional) {
                form.setValue("name", t.additional.value);
                form.setValue("address", t.additional.address ?? "");
                form.setValue("postalCode", t.additional.postalCode ?? "");
                form.setValue("city", t.additional.city ?? "");
                // form.setValue("capital", BigInt(t.additional.capital || 0));
                form.setValue("phone", t.additional.phone ?? "");
                form.setValue("codeNaf", t.additional.codeNaf ?? "");
                form.setValue("email", t.additional.email ?? "");
                form.setValue(
                  "legalFormId",
                  Number(t.additional.legalFormId) ?? 0
                );
              }
            }}
            className="focus:!ring-blue-500"
          />
          <button type="submit" className="sr-only" ref={ref} />
          <Dialog open={isOpen} onOpenChange={handleOpenChange}>
            <DialogTrigger asChild className="mt-6 w-full">
              <Button
                className="w-full"
                type="button"
                loading={form.formState.isSubmitting}
                disabled={!nameValue && !sirenValue}
              >
                {t("organizations.createForm.submit")}
              </Button>
            </DialogTrigger>
            <OrganizationRegistrationModal
              loading={loading}
              onSubmit={() => {
                if (ref.current) {
                  ref.current.click();
                } else {
                  onSubmit();
                }
              }}
              onClose={() => setIsOpen(false)}
            />
          </Dialog>
        </form>
      </Form>
    </div>
  );
}
