"use client";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { sessionQuery<PERSON>ey, useSessionQuery } from "@saas/auth/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { type ReactNode, useEffect, useState } from "react";
import { signOutAndClearCache } from "../lib/helpers";
import { SessionContext } from "../lib/session-context";

export function SessionProvider({ children }: { children: ReactNode }) {
  const queryClient = useQueryClient();
  const router = useRouter();

  const { data: session } = useSessionQuery();
  const [loaded, setLoaded] = useState(!!session);

  const reloadSession = async () => {
    const { data: newSession, error } = await authClient.getSession({
      query: {
        disableCookieCache: true,
      },
    });

    if (error) {
      throw new Error(error.message || "Failed to fetch session");
    }

    queryClient.setQueryData(sessionQueryKey, () => newSession);
  };

  useEffect(() => {
    if (session && !loaded) {
      setLoaded(true);
    } else if (!session?.user) {
      signOutAndClearCache({
        onSuccess: async () => {
          await reloadSession();
          router.push(config.auth.redirectAfterLogout || "/auth/login");
        },
      });
    }
  }, [session]);

  return (
    <SessionContext.Provider
      value={{
        loaded,
        session: session?.session ?? null,
        user: session?.user ?? null,
        reloadSession: reloadSession,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}
