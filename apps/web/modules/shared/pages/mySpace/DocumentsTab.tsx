import { TFile } from "@repo/database/client";
import { DocumentViewerDialog } from "@shared/components/common/documents";
import { FeatureFlag } from "@shared/components/common/FeatureFlag";
import { TreeTable } from "@shared/components/common/TreeTable";
import { Column } from "@shared/components/common/types";
import { useTFile } from "@shared/hooks/models";
import { useCurrentOrganization } from "@shared/hooks/models/custom/useCurrentOrganization";
import { useListSearchParams } from "@shared/hooks/useListSearchParams";
import { useLocalStorageFilter } from "@shared/hooks/useLocalStorageFilter";
import { Info } from "lucide-react";
import { PUBLIC_FILES } from "node_modules/@repo/api/src/trpc/routers/custom/TFile.router";
import { useState } from "react";
import { DocumentCategoriesInfo } from "./DocumentsTab/DocumentCategoriesInfo";
import { DocumentFilterSection } from "./DocumentsTab/DocumentFilterSection";
import {
  CompanyDocumentsButton,
  PersonalDocumentsButton,
  ServiceDocumentsButton,
} from "./DocumentsTab/GridButtons";
import { type DocumentType, MyDocuments } from "./DocumentsTab/MyDocuments";

export function DocumentsTab() {
  const [openDocumentViewerDialog, setOpenDocumentViewerDialog] =
    useState<boolean>(false);
  const [selectedFileId, setSelectedFileId] = useState<string>();
  const { data: org } = useCurrentOrganization();

  const { listParams } = useListSearchParams();
  const { filters } = useLocalStorageFilter();
  const { list: file, byId } = useTFile({
    ...listParams,
    filters,
    id: selectedFileId,
    disableLists: !org?.id,
    where: {
      typeId: { notIn: PUBLIC_FILES },
    },
  });

  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [documentType, setDocumentType] = useState<DocumentType>("personal");
  const [isTreeView, setIsTreeView] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCategoriesInfo, setShowCategoriesInfo] = useState(false);
  const [filtersChange, setFiltersChange] = useState<{
    type?: string[];
    dateRange?: { start: string; end: string } | null;
    size?: "small" | "medium" | "large" | null;
  }>({});

  const columns: Column<TFile>[] = [
    {
      key: "name",
      header: "Nom du fichier",
      width: 248,
      sortable: true,
      align: "left",
      render: (row) => row.name,
    },
    {
      key: "type",
      header: "Type de fichier",
      width: 248,
      sortable: true,
      align: "left",
      render: (row: any) => row.type?.label,
    },
    {
      key: "createdAt",
      header: "Date de création",
      width: 180,
      sortable: true,
      align: "left",
      render: (row) =>
        row.createdAt ? new Date(row.createdAt).toLocaleDateString() : "",
    },
  ];

  const listFile = (file.data?.items ?? []) as TFile[];

  const handleDocumentClick = (type: Exclude<DocumentType, null>) => {
    setDocumentType(type);
  };

  return (
    <div className="space-y-6">
      <FeatureFlag flag="enablePersonalDocuments">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-medium text-gray-900">Documents</h2>
          <button
            onClick={() => setShowCategoriesInfo(true)}
            className="flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Info className="w-4 h-4" />
            <span>Comprendre les catégories</span>
          </button>
        </div>
        <div className="grid grid-cols-3 gap-6">
          <PersonalDocumentsButton
            onClick={() => handleDocumentClick("personal")}
            isActive={documentType === "personal"}
          />
          <ServiceDocumentsButton
            onClick={() => handleDocumentClick("service")}
            isActive={documentType === "service"}
          />
          <CompanyDocumentsButton
            onClick={() => handleDocumentClick("company")}
            isActive={documentType === "company"}
          />
        </div>
      </FeatureFlag>
      <DocumentFilterSection
        onSearch={setSearchTerm}
        onFilterChange={setFiltersChange}
        isTreeView={isTreeView}
        onViewChange={setIsTreeView}
      />
      {documentType !== "company" && (
        <MyDocuments
          type={documentType}
          isTreeView={isTreeView}
          searchTerm={searchTerm}
          filters={filtersChange}
        />
      )}

      {documentType === "company" && (
        <TreeTable
          data={listFile}
          columns={columns}
          selectedRows={selectedRows}
          onRowSelectChange={({ selectedRows }) => {
            setSelectedRows(selectedRows);
          }}
          onRowClick={(rowId) => {
            setSelectedFileId(rowId);
            setOpenDocumentViewerDialog(true);
          }}
          pagination={file.data?.pagination}
        />
      )}

      {byId.data && openDocumentViewerDialog && (
        <DocumentViewerDialog
          file={byId.data}
          isOpen={openDocumentViewerDialog}
          onClose={() => {
            setSelectedFileId(undefined);
            setOpenDocumentViewerDialog(false);
          }}
        />
      )}

      <DocumentCategoriesInfo
        open={showCategoriesInfo}
        onClose={() => setShowCategoriesInfo(false)}
      />
    </div>
  );
}
