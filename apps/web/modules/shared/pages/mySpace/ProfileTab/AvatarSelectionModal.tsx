import { Dialog } from "@mui/material";
import { useTFile } from "@shared/hooks/models";
import { Button } from "@ui/components/button";
import { X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface AvatarSelectionModalProps {
  open: boolean;
  onClose: () => void;
  selectedService?: string;
  onSelect: (serviceId: string) => void;
}

export function AvatarSelectionModal({
  open,
  onClose,
  selectedService,
  onSelect,
}: AvatarSelectionModalProps) {
  const videoRefs = useRef<HTMLVideoElement[]>([]);
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(
    selectedService || null
  );

  const { list } = useTFile({
    filters: {
      typeId: "avatar",
      organizationId: null,
    },
    take: 100,
  });

  const avatars = list.data?.items;

  useEffect(() => {
    if (open) setSelectedAvatar(selectedService || null);
  }, [selectedService, open]);

  useEffect(() => {
    videoRefs.current.forEach((video, i) => {
      if (!avatars || !video || !selectedAvatar) return;
      if (selectedAvatar === avatars[i].id) {
        video.play();
        video.muted = false;
      } else {
        video.pause();
        video.currentTime = 0;
      }
    });
  }, [selectedAvatar]);

  const handleSave = () => {
    if (!selectedAvatar) return;
    onSelect(selectedAvatar);
    setTimeout(onClose, 100); // Laisser le temps à la sélection de se propager
  };

  if (!avatars) return null;

  return (
    <Dialog
      open={open}
      onClose={() => {
        onClose();
        setSelectedAvatar(null);
      }}
      maxWidth="md"
      fullWidth
    >
      <div className="p-6 relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold">Choisir un avatar</h2>
            <p className="text-sm text-gray-600">
              Sélectionnez un avatar animé pour votre profil
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Grid d'avatars */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 overflow-auto max-h-[48rem] pb-20 px-4">
          {avatars.map((service, i) => (
            <button
              key={service.id}
              onClick={() => {
                setSelectedAvatar(service.id);
              }}
              onDoubleClick={() => {
                setSelectedAvatar(service.id);
                handleSave();
              }}
              className={`p-4 rounded-lg border transition-colors ${
                selectedAvatar === service.id
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-blue-200"
              }`}
            >
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 mb-3">
                <video
                  ref={(el) => {
                    if (el) {
                      videoRefs.current[i] = el;
                    }
                  }}
                  className="w-full h-full object-cover"
                  autoPlay={selectedAvatar === service.id}
                  muted={selectedAvatar !== service.id}
                  playsInline
                >
                  <source src={service.url || ""} type="video/mp4" />
                </video>
              </div>
              <div className="text-sm font-medium text-center">
                {service.name}
              </div>
            </button>
          ))}
        </div>

        <div className="absolute bottom-0 left-0 w-full flex justify-end p-6 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)]">
          <Button
            disabled={!selectedAvatar || selectedAvatar === selectedService}
            onClick={handleSave}
          >
            Sauvegarder
          </Button>
        </div>
      </div>
    </Dialog>
  );
}
