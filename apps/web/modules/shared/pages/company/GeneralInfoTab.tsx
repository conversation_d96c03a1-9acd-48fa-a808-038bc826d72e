import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@mui/material";
import { TCompanyContactUpdateSchema } from "@repo/api/validation";
import { FeatureFlag } from "@shared/components/common/FeatureFlag";
import { Form } from "@shared/components/common/form";
import { LabelledAddressField } from "@shared/components/common/textfield/LabelledAddressField";
import { LabelledEmailVFTF } from "@shared/components/common/textfield/LabelledEmailVFTF";
import { LabelledPhoneVFTF } from "@shared/components/common/textfield/LabelledPhoneVFTF";
import { LabelledSelectMultipleInput } from "@shared/components/common/textfield/LabelledSelectMultipleInput";
import { LabelledSirenVFTF } from "@shared/components/common/textfield/LabelledSirenVFTF";
import { LabelledUrlField } from "@shared/components/common/textfield/LabelledUrlField";
import { LabelledVTextField } from "@shared/components/common/textfield/LabelledVTextField";
import { CompanyLevels } from "@shared/components/company/CompanyLevels";
import {
  useLCodeNaf,
  useLCollectiveAgreement,
  useLLegalForm,
  useTCompanyContact,
} from "@shared/hooks/models";
import {
  Chart as ChartJS,
  Filler,
  Legend,
  LineElement,
  PointElement,
  RadialLinearScale,
  Tooltip,
} from "chart.js";
import { useLocation } from "custom-router";
import { Building2, FileText, MapPin, Trophy } from "lucide-react";
import Link from "next/link";
import { useEffect, useRef } from "react";
import { Radar } from "react-chartjs-2";
import { useForm } from "react-hook-form";

// Register required chart components
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

// Chart configuration
const skillsData = {
  labels: [
    "Décor",
    "Enduits",
    "Patines",
    "Finition",
    "Rapidité",
    "Propreté",
    "Organisation",
  ],
  datasets: [
    {
      label: "Compétences",
      data: [9, 8, 9, 8.5, 7.5, 8, 8.5],
      backgroundColor: "rgba(59, 130, 246, 0.2)",
      borderColor: "rgb(59, 130, 246)",
      borderWidth: 2,
      pointBackgroundColor: "rgb(59, 130, 246)",
      pointBorderColor: "#fff",
      pointHoverBackgroundColor: "#fff",
      pointHoverBorderColor: "rgb(59, 130, 246)",
    },
  ],
};

const skillsOptions = {
  scales: {
    r: {
      angleLines: {
        display: true,
        color: "rgba(0, 0, 0, 0.1)",
      },
      suggestedMin: 0,
      suggestedMax: 10,
      ticks: {
        stepSize: 2,
        callback: (value: number) => value.toString(),
      },
      pointLabels: {
        font: {
          size: 12,
          weight: "500" as const,
        },
      },
    },
  },
  plugins: {
    legend: {
      display: false,
    },
  },
  maintainAspectRatio: false,
};

// type CompanyInfo = {
//   date_creation: string;
//   date_immatriculation_rne: string;
// };

interface GeneralInfoTabProps {
  loading?: boolean;
}
export function GeneralInfoTab(_?: GeneralInfoTabProps) {
  const progressionRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  const { activeCompanyContact, update } = useTCompanyContact();

  const {
    register,
    control,
    formState: { errors, isSubmitting },
    setValue,
    getValues,
    handleSubmit,
  } = useForm({
    defaultValues:
      activeCompanyContact?.data ?? ({} as any) /* TODO remove any */,
    resolver: zodResolver(TCompanyContactUpdateSchema),
  });

  const { sirenNumber, creationDate, registrationDateRne } = getValues();
  const { autocomplete: codeNafOptions } = useLCodeNaf({ autocomplete: true });
  const { autocomplete: legalFormOptions } = useLLegalForm({
    autocomplete: true,
  });
  const { autocomplete: collectiveAgreement } = useLCollectiveAgreement({
    autocomplete: true,
  });

  // replicate shared/pages/Company.tsx progressionRef usage as the props can't be passed
  useEffect(() => {
    const state = location.state as any as {
      activeTab?: number;
      section?: string;
    };

    if (state?.activeTab !== undefined && state.section === "progression") {
      setTimeout(() => {
        progressionRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  }, [location]);

  return (
    <Form onSubmit={handleSubmit(update)}>
      <div className="space-y-6">
        {/* État des inscriptions */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
            <FileText className="h-5 w-5" />
            État des inscriptions
          </h2>
          <div className="bg-gray-50 p-4 rounded-lg space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* INSEE */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  INSEE
                </h3>
                <div className="bg-white p-3 rounded border border-gray-200">
                  {creationDate && (
                    <p className="text-sm text-gray-600">
                      Inscrite le {creationDate}
                    </p>
                  )}
                  <div className="mt-2">
                    <Link
                      target="_blank"
                      rel="noopener noreferrer"
                      href={`https://sirene.fr/sirene/public/recherche?recherche.sirenSiret=${sirenNumber}`}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Voir l'avis de situation
                    </Link>
                  </div>
                </div>
              </div>

              {/* RNE */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  RNE (INPI)
                </h3>
                <div className="bg-white p-3 rounded border border-gray-200">
                  {registrationDateRne && (
                    <p className="text-sm text-gray-600">
                      Immatriculée le {registrationDateRne}
                    </p>
                  )}
                  <div className="mt-2">
                    <Link
                      target="_blank"
                      rel="noopener noreferrer"
                      href={`https://data.inpi.fr/entreprises/${sirenNumber}`}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Voir l'extrait RNE
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Informations principales */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
            <Building2 className="h-5 w-5" />
            Informations principales
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <LabelledVTextField
                label="Dénomination"
                width="w-full"
                {...register("name")}
              />
              <LabelledSirenVFTF
                label="SIREN"
                disabled
                {...register("sirenNumber")}
              />
              <LabelledVTextField
                label="SIRET du siège social"
                // {...register("siretNumber")}
                width="w-full"
              />
              <LabelledVTextField
                label="N° TVA Intracommunautaire"
                // {...register("tvaNumber")}
                width="w-full"
              />
            </div>
            <div>
              <LabelledSelectMultipleInput
                isMulti={false}
                label="Forme juridique"
                name="legalFormId"
                control={control}
                options={legalFormOptions.options ?? []}
                placeholder="Sélectionner..."
                className="w-full"
              />
              <LabelledSelectMultipleInput
                isMulti={false}
                label="Code NAF"
                name="codeNaf"
                control={control}
                options={codeNafOptions.options ?? []}
                placeholder="Sélectionner un code NAF"
                className="w-full"
              />
              <LabelledSelectMultipleInput
                isMulti={true}
                label="Convention(s) collective(s)"
                name="collectiveAgreements"
                control={control}
                options={collectiveAgreement.options ?? []}
                placeholder="Sélectionner..."
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Coordonnées */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
            <MapPin className="h-5 w-5" />
            Coordonnées
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <LabelledAddressField
                register={register}
                setValue={setValue}
                control={control}
              />
            </div>
            <div>
              <LabelledPhoneVFTF
                label="Téléphone"
                control={control}
                name="phone"
              />
              <LabelledEmailVFTF label="Email" {...register("email")} />
              <LabelledUrlField
                label="Site web"
                {...register("website")}
                width="w-full"
              />
            </div>
          </div>
        </div>

        {/* Radar Chart des compétences */}
        <FeatureFlag flag="enableEmployeeSkills">
          <div>
            <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
              <FileText className="h-5 w-5" />
              Compétences
            </h2>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div style={{ height: "400px" }}>
                <Radar
                  data={skillsData as any}
                  options={skillsOptions as any}
                />
              </div>
            </div>
          </div>
        </FeatureFlag>

        {/* Progression de l'entreprise */}
        <FeatureFlag flag="enableRewards">
          <div ref={progressionRef}>
            <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
              <Trophy className="h-5 w-5" />
              Progression de l'entreprise
            </h2>
            <CompanyLevels />
          </div>
        </FeatureFlag>

        <div className="flex justify-end">
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Enregistrement..." : "Enregistrer"}
          </Button>
        </div>
      </div>
    </Form>
  );
}
