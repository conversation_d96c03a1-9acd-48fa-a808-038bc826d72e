"use client";
import { trpc } from "@repo/api/client";
import type { LDocumentKey, Prisma, TFile } from "@repo/database/client";
import { removeEmptyValues } from "@repo/utils";
import { prepareFilters } from "@shared/utils/filterSections";
import { useToast } from "@ui/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

export type UseTFileProps = {
  id?: any;
  statsQuery?: any;
  autocomplete?: any;
  filters?: any;
  urlToSign?: string;
  disableLists?: boolean;
  onSuccess?: (props: {
    tempId: number;
    data: UploadedFile;
    tFile: TFile;
    documentTypeKey: LDocumentKey;
  }) => Promise<void>;
  onLoad?: (props: { tempId: number; file: any }) => void;
  onProgress?: (props: {
    tempId: number;
    isUploading: boolean;
    percentLoaded: string;
    total: number;
    loaded: number;
  }) => void;
  onError?: (props: { tempId: number; error: string }) => void;
  take?: number;
  where?: Prisma.TFileWhereInput;
};

export type UploadedFile = {
  bytes: number;
  etag: string | null;
  url: string;
  filename: string;
  typeId: LDocumentKey;
};

export const useTFile = (
  {
    id,
    statsQuery,
    autocomplete: autocompleteOption,
    urlToSign,
    onSuccess,
    onLoad,
    onProgress,
    onError,
    ...options
  } = {} as UseTFileProps
) => {
  const listOptions = removeEmptyValues({
    ...options,
    where: {
      ...options.where,
      ...(options.filters ? prepareFilters("AND", options.filters) : {}),
    },
  });

  const list = trpc.tFile.findMany.useQuery(listOptions, {
    enabled: !autocompleteOption && !options.disableLists,
  });

  const autocomplete = trpc.tFile.findMany.useQuery(
    {
      ...listOptions,
      page: 1,
      take: 1_000,
    },
    { enabled: !!autocompleteOption }
  );

  const byId = trpc.tFile.findOne.useQuery(
    { id: id as string },
    { enabled: !!id }
  );

  const stats = trpc.tFile.stats.useQuery(statsQuery);

  const utils = trpc.useUtils();
  const { toast } = useToast();
  const t = useTranslations();

  const create = trpc.tFile.create.useMutation({
    onSuccess: (_data) => {
      toast({
        variant: "success",
        title: t("common.action.saved"),
      });
      utils.tFile.findMany.invalidate();
      utils.tFile.stats.invalidate();
    },
  });

  const update = trpc.tFile.update.useMutation({
    onSuccess: (_data) => {
      toast({
        variant: "success",
        title: t("common.action.saved"),
      });
      utils.tFile.findOne.invalidate();
      utils.tFile.findMany.invalidate();
    },
  });

  const deleteMany = trpc.tFile.deleteMany.useMutation({
    onSuccess: (_data) => {
      toast({
        variant: "success",
        title: t("common.action.deleted"),
      });
      utils.tFile.findMany.invalidate();
      utils.tFile.stats.invalidate();
    },
  });

  /**
   * Signed Upload and Signed download
   *
   * This signUploadUrl directly create an empty tFile,
   * needed to generate the filename.
   * As seen on the implementation of `handleOnload` bellow, the tFile get populated
   */
  const signUploadUrl = trpc.tFile.signUploadUrl.useMutation();
  const signDownloadUrl = trpc.tFile.signDownloadUrl.useMutation();

  const [signedUrl, setSignedUrl] = useState<string | null>(null);

  useEffect(() => {
    if (urlToSign?.startsWith("https://")) {
      signDownloadUrl.mutateAsync({ url: urlToSign }).then((data) => {
        setSignedUrl(data.signedUrl);
      });
    }
  }, [urlToSign]);

  async function uploadAsync({
    file,
    tempId: _tempId,
    documentTypeKey,
    expiresAt,
    userId,
    documentDate,
  }: {
    file: any;
    tempId?: number;
    documentTypeKey: LDocumentKey;
    expiresAt?: Date | null;
    documentDate?: Date | null;
    userId?: string | null;
  }) {
    const tempId = _tempId ?? Date.now();

    if (onLoad) {
      const reader = new FileReader();
      reader.onload = () => onLoad({ tempId, file });
      reader.readAsDataURL(file);
    }

    const signedData = await signUploadUrl.mutateAsync({
      filename: file.name,
      documentTypeKey,
    });

    function handleOnProgress({ total, loaded }: any) {
      const percentLoaded = Math.round((loaded / total) * 100).toFixed(2);
      onProgress?.({
        tempId,
        isUploading: true,
        percentLoaded,
        total,
        loaded,
      });
    }

    const uploadResult = await new Promise<{
      data: UploadedFile;
      tFile: any;
      tempId: number;
      documentTypeKey: LDocumentKey;
    }>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("PUT", signedData.presignedUrl, true);
      xhr.setRequestHeader("Content-Type", file.type);

      xhr.upload.addEventListener("progress", handleOnProgress);
      xhr.addEventListener("loadstart", () => console.debug("Upload start."));
      xhr.addEventListener("load", () => console.debug("Upload load"));
      xhr.addEventListener("loadend", () => console.debug("Upload end."));

      xhr.addEventListener("load", async () => {
        try {
          const data: UploadedFile = {
            bytes:
              Number.parseInt(
                xhr.getResponseHeader("content-length") || "0",
                10
              ) || file.size,
            etag: null,
            url: signedData.url,
            filename: file.name,
            typeId: signedData.typeId,
          };
          const updatedTFile = await update.mutateAsync({
            id: signedData.tFileId,
            url: data.url,
            name: data.filename,
            filename: data.filename,
            bytes: data.bytes,
            expiresAt,
            userId,
            documentDateTz: documentDate,
          });
          resolve({ data, tFile: updatedTFile, tempId, documentTypeKey });
        } catch (err) {
          reject(err);
        }
      });

      xhr.addEventListener("error", () => {
        onError?.({ tempId, error: "Upload error." });
        reject(new Error("Upload error."));
      });

      xhr.addEventListener("abort", () => {
        console.error("Upload cancelled.");
        reject(new Error("Upload cancelled."));
      });

      xhr.send(file);
    });

    try {
      void onSuccess?.({
        tempId,
        data: uploadResult.data,
        tFile: uploadResult.tFile,
        documentTypeKey,
      });
    } catch (err) {
      console.error("Post-upload: onSuccess callback failed with:", err);
    }

    return uploadResult;
  }

  return {
    list,
    autocomplete: {
      options: autocomplete?.data?.items.map((item) => ({
        label: item.name,
        value: item.id,
      })),
      loading: autocomplete?.isLoading,
    },
    byId,
    stats,
    create: create.mutateAsync,
    createdItem: create.data,
    update: update.mutateAsync,
    updatedItem: update.data,
    deleteMany: deleteMany.mutateAsync,
    // Signed Upload and Signed download
    uploadAsync,
    signedUrl,
  };
};
