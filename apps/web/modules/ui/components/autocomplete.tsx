import { InfoPapperResult } from "@repo/types";
import { cn } from "@ui/lib";
import { Command as CommandPrimitive } from "cmdk";
import { Check } from "lucide-react";
import {
  type KeyboardEvent,
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "./command";
import { Skeleton } from "./skeleton";

export type Option = {
  capital?: string;
  address?: string;
  city?: string;
  phone?: string;
  label: string;
  legalForm?: string;
  postalCode?: string;
  codeNaf?: string;
  legalFormId?: number;
  email?: string;
  value: string;
  render?: ReactNode;
  additional?: Option;
  companyInfoFromPappers?: InfoPapperResult;
};

type AutoCompleteProps = {
  options: Option[];
  emptyMessage: string;
  value?: Option;
  onValueChange?: (value: Option) => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  onSearchChange?: (search: string) => void;
  name?: string;
  resetOnSelect?: boolean;
  disableSelected?: boolean;
  showListOnFocus?: boolean;
};

export const AutoComplete = ({
  className,
  options,
  placeholder,
  emptyMessage,
  value,
  onValueChange,
  disabled,
  isLoading = false,
  onFocus,
  onBlur,
  onSearchChange,
  name,
  resetOnSelect,
  disableSelected,
  showListOnFocus,
}: AutoCompleteProps) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const [isOpen, setOpen] = useState(false);
  const [selected, setSelected] = useState<Option>(value as Option);
  const [inputValue, setInputValue] = useState<string>(value?.label || "");

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLDivElement>) => {
      const input = inputRef.current;
      if (!input) {
        return;
      }

      // Keep the options displayed when the user is typing
      if (!isOpen) {
        setOpen(true);
      }

      // This is not a default behaviour of the <input /> field
      if (event.key === "Enter" && input.value !== "") {
        const optionToSelect = options.find(
          (option) => option.label === input.value
        );
        if (optionToSelect) {
          setSelected(optionToSelect);
          onValueChange?.(optionToSelect);
        }
      }

      if (event.key === "Escape") {
        input.blur();
      }
    },
    [isOpen, options, onValueChange]
  );

  const handleChange = (search: string) => {
    onSearchChange?.(search);
    setInputValue(search);
  };

  const handleBlur = useCallback(() => {
    setOpen(false);
    setInputValue(selected?.label);
    onBlur?.();
  }, [selected]);

  const handleSelectOption = useCallback(
    (selectedOption: Option) => {
      setSelected(undefined as any);
      setInputValue(resetOnSelect ? "" : selectedOption.label);
      if (!resetOnSelect) {
        setSelected(selectedOption);
      }
      onValueChange?.(selectedOption);

      // This is a hack to prevent the input from being focused after the user selects an option
      setTimeout(() => {
        inputRef?.current?.blur();
      }, 0);
    },
    [onValueChange]
  );

  useEffect(() => {
    if (value) {
      setOpen(false);
      setSelected(value);
      setInputValue(value.value);
      if (inputRef.current) inputRef.current.blur();
    }
  }, [value]);

  return (
    <CommandPrimitive
      onKeyDown={handleKeyDown}
      filter={(_value, _search) => {
        // options are filtered and provided by the user
        return 1;
      }}
    >
      <div className="border border-gray-300">
        <CommandInput
          name={name}
          ref={inputRef}
          value={inputValue}
          onValueChange={handleChange}
          onBlur={handleBlur}
          onFocus={() => {
            onFocus?.();
            if (showListOnFocus) setOpen(true);
          }}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "px-3 py-2 text-sm border bg-white text-gray-900 disabled:bg-gray-100 disabled:text-gray-500 placeholder:text-gray-400 transition-colors duration-200",
            className
          )}
        />
      </div>
      <div className="relative mt-1">
        <div
          className={cn(
            "animate-in fade-in-0 zoom-in-95 absolute top-0 z-10 w-full rounded-xl bg-white outline-none",
            isOpen ? "block" : "hidden"
          )}
        >
          <CommandList className="rounded-lg ring-1 ring-slate-200">
            {isLoading ? (
              <CommandPrimitive.Loading>
                <div className="p-1 relative">
                  <Skeleton className="h-8 w-full" />
                  <p className="absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-sm animate-pulse">
                    Chargement...
                  </p>
                </div>
              </CommandPrimitive.Loading>
            ) : null}
            {options.length > 0 && !isLoading ? (
              <CommandGroup>
                {options.map((option, key) => {
                  const isSelected = selected?.value === option.value;
                  return (
                    <CommandItem
                      key={`${key}-${option.value}`}
                      value={`${key}-${option.value}`}
                      onMouseDown={(event) => {
                        event.preventDefault();
                        event.stopPropagation();
                      }}
                      onSelect={() => handleSelectOption(option)}
                      className={cn(
                        "flex w-full items-center gap-2 hover:cursor-pointer",
                        !isSelected ? "pl-8" : null
                      )}
                    >
                      {!disableSelected && isSelected ? (
                        <Check className="w-4" />
                      ) : null}
                      {option?.render ?? option?.label}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            ) : null}
            {!isLoading ? (
              <CommandPrimitive.Empty className="select-none rounded-sm px-2 py-3 text-center text-sm">
                {emptyMessage}
              </CommandPrimitive.Empty>
            ) : null}
          </CommandList>
        </div>
      </div>
    </CommandPrimitive>
  );
};
